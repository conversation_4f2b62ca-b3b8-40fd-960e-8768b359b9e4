[run]
source = mxgo
omit =
    */tests/*
    */test_*
    mxgo/db/alembic/env.py
    mxgo/db/alembic/versions/*
    mxgo/scheduler_runner.py
    mxgo/scripts/run_*.py
    */migrations/*
    */__pycache__/*
    */.*

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

[html]
directory = htmlcov
