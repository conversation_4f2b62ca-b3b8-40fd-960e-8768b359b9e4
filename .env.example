# =============================================================================
# MXGo Environment Configuration
# =============================================================================
# Copy this file to .env and configure your values
# See README.md and DOCKER_SETUP.md for detailed setup instructions

# =============================================================================
# 🔧 CORE APPLICATION (Required)
# =============================================================================
# Basic application configuration required for startup

# Server configuration
PORT=8000
HOST=0.0.0.0
LOG_LEVEL=INFO
IS_PROD=false
WHITELIST_ENABLED=false
X_API_KEY=your_secure_api_key_here

# For /suggestions endpoint via chrome extension
JWT_SECRET=your_jwt_secret_key_here

# =============================================================================
# 🤖 AI MODELS (Required)
# =============================================================================
# Configure AI models via model.config.toml - these env vars provide defaults

# LiteLLM Router Configuration
LITELLM_CONFIG_PATH=model.config.toml
LITELLM_DEFAULT_MODEL_GROUP=gpt-4 # This is IMPORTANT for which model group in routed LLM your requests should go to

# SiliconFlow API Configuration (Required for AI models)
SILICONFLOW_API_KEY=your_siliconflow_api_key_here

# Hugging Face Token (required for smolagents)
HF_TOKEN=your_huggingface_token_here

# =============================================================================
# 💾 INFRASTRUCTURE SERVICES
# =============================================================================
# Database, cache, and message queue configuration

# PostgreSQL Database (required)
DB_HOST=postgres
DB_PORT=5432
DB_NAME=mxgo
DB_USER=mxgo
DB_PASSWORD=your_secure_db_password

# RabbitMQ Message Queue (required)
RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672
RABBITMQ_USER=mxgo_user
RABBITMQ_PASSWORD=your_rabbitmq_password
RABBITMQ_VHOST=/
RABBITMQ_HEARTBEAT=60

# Supabase (optional if using whitelist and validation)
SUPABASE_URL=your_supabase_project_url
SUPABASE_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Redis Cache (Optional - for rate limiting)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your_redis_password

# =============================================================================
# 📧 EMAIL SERVICE (Required for sending responses)
# =============================================================================
# Email provider configuration - choose 'aws_ses' or 'outlook'
EMAIL_PROVIDER=outlook

# AWS SES configuration (if using aws_ses provider)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
SENDER_EMAIL=<EMAIL>

# Microsoft Graph API configuration (if using outlook provider)
MICROSOFT_CLIENT_ID=your_microsoft_client_id_here
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret_here
MICROSOFT_TENANT_ID=common
OUTLOOK_SELECTED_MAILBOX=<EMAIL>

# =============================================================================
# 🔍 SEARCH SERVICES (Optional)
# =============================================================================
# Web search providers - choose based on quality/cost needs

# Google Search APIs (Premium - highest quality)
# Get from https://serpapi.com/
SERPAPI_API_KEY=

# Get from https://serper.dev/
SERPER_API_KEY=

# Brave Search API (Moderate cost - good quality)
BRAVE_SEARCH_API_KEY=

# =============================================================================
# 🔗 EXTERNAL APIS (Optional)
# =============================================================================
# Third-party services for enhanced functionality

# Jina AI (for deep research functionality)
JINA_API_KEY=

# RapidAPI (for external API data and other services)
RAPIDAPI_KEY=

# =============================================================================
# 💳 PAYMENT INTEGRATION (Optional)
# =============================================================================
# Dodo Payments integration for user plan management

# Dodo Payments API key for customer and subscription lookups
DODO_API_KEY=your_dodo_payments_api_key_here

# Product ID for PRO plan identification in Dodo Payments
PRO_PLAN_PRODUCT_ID=your_pro_plan_product_id_here

# =============================================================================
# 📊 MONITORING & OBSERVABILITY (Optional)
# =============================================================================
# Logging and monitoring services

# Logfire for advanced logging (optional)
LOGFIRE_TOKEN=

# =============================================================================
# ⚙️ SCHEDULER & WORKER CONFIG (Optional)
# =============================================================================
# Task scheduling and background processing

SCHEDULER_API_BASE_URL=http://api_server:8000
SCHEDULER_API_TIMEOUT=300

# =============================================================================
# 🛠️ MCP TOOLS (Optional - Feature is in progress)
# =============================================================================
# Model Context Protocol configuration

MXGO_ENABLE_MCP=true
MXGO_MCP_CONFIG_PATH=mcp.toml
MXGO_MCP_TIMEOUT=30

# =============================================================================
# 🌐 FRONTEND & EXTERNAL URLS (Optional)
# =============================================================================
# Frontend and external service URLs

WHITELIST_SIGNUP_URL=https://mxgo.ai/whitelist
FRONTEND_URL=https://mxgo.ai/

# =============================================================================
# 🧪 DEVELOPMENT & TESTING (Development Only)
# =============================================================================
# Variables used only in development/testing

# Uncomment for testing
# TEST_EMAIL=<EMAIL>

# =============================================================================
# 🐳 DOCKER DEVELOPMENT OVERRIDES (Docker Only)
# =============================================================================
# These override the defaults above for Docker development
# Uncomment and modify only if you need different values for Docker

# API_PORT=8000
# RABBITMQ_MANAGEMENT_PORT=15672
# =============================================================================
# 🐳 DOCKER DEVELOPMENT OVERRIDES (Docker Only)
# =============================================================================
# These override the defaults in docker-compose.yml for Docker development
# Uncomment and modify only if you need different values for Docker

# Port Overrides
# API_PORT=8000                    # External API port (default: 8000)
# DB_PORT=5432                     # PostgreSQL port (default: 5432)
# REDIS_PORT=6379                  # Redis port (default: 6379)
# RABBITMQ_PORT=5672               # RabbitMQ AMQP port (default: 5672)
# RABBITMQ_MANAGEMENT_PORT=15672   # RabbitMQ web UI port (default: 15672)

# Service Authentication Overrides
# DB_NAME=mxgo                   # Database name (default: mxgo)
# DB_USER=mxgo                   # Database user (default: mxgo)
# DB_PASSWORD=your_db_password     # Database password (default: docker_changeme_123)
# REDIS_PASSWORD=your_redis_pass   # Redis password (default: docker_redis_123)
# RABBITMQ_USER=your_rmq_user      # RabbitMQ user (default: docker_guest)
# RABBITMQ_PASSWORD=your_rmq_pass  # RabbitMQ password (default: docker_guest_123)
