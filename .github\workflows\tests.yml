name: Tests

on:
  push:
    branches:
      - "*"
    paths:
      - '**.py'
      - 'pyproject.toml'
      - 'poetry.lock'
      - '.github/actions/test.yml'

concurrency:
  group: mxgo-tests
  cancel-in-progress: false

jobs:
  backend-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    name: backend tests

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: testpassword
          POSTGRES_USER: testuser
          POSTGRES_DB: testdb
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    env:
      X_API_KEY: "some-api-key"
      SUGGESTIONS_API_KEY: "another-api-key"
      IS_PROD: "true"
      LITELLM_DEFAULT_MODEL_GROUP: ${{ secrets.LITELLM_DEFAULT_MODEL_GROUP }}
      # Test database configuration
      TEST_DB_URL: postgresql://testuser:testpassword@localhost:5432/testdb
      JWT_SECRET_KEY: "test-jwt-secret-key"

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        id: setup-python
        uses: actions/setup-python@v5
        with:
          python-version: 3.12

      - name: Load cached Poetry installation
        id: cached-poetry
        uses: actions/cache@v4
        with:
          path: ~/.local
          key: poetry-${{ runner.os }}-${{ hashFiles('**/poetry.lock') }}

      - name: Install Poetry
        if: steps.cached-poetry.outputs.cache-hit != 'true'
        uses: snok/install-poetry@v1
        with:
          virtualenvs-create: true
          virtualenvs-in-project: true
          virtualenvs-path: .venv
          installer-parallel: true

      - name: Load cached venv
        id: cached-poetry-dependencies
        uses: actions/cache@v4
        with:
          path: .venv
          key: venv-${{ runner.os }}-${{ steps.setup-python.outputs.python-version }}-${{ hashFiles('**/poetry.lock') }}

      - name: Install dependencies
        if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
        run: poetry install --no-interaction --with dev

      - name: Create LiteLLM config from secret
        env:
          MODEL_CONFIG_CONTENT: ${{ secrets.MODEL_CONFIG }}
        run: |
          echo "$MODEL_CONFIG_CONTENT" > litellm_config.toml
          echo "LITELLM_CONFIG_PATH=$(pwd)/litellm_config.toml" >> $GITHUB_ENV
          echo "Created litellm_config.toml and set LITELLM_CONFIG_PATH"

      - name: Run tests
        run: poetry run pytest -v --timeout 180 --cov-report=term-missing:skip-covered --cov-report=html:htmlcov --cov=mxgo --log-cli-level=INFO --pytest-durations-min=5

      - name: Upload pytest report
        uses: actions/upload-artifact@v4
        with:
          name: pytest-report
          path: pytest.xml

      - name: Upload coverage report
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: htmlcov/
