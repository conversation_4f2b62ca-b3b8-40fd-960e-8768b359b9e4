# Contributing to MXGo

Thank you for your interest in contributing to m<PERSON><PERSON>, an intelligent email assistant that processes emails automatically using AI.

## Setting up the project

Please follow the instructions in the README.md file to set up the project.

## Making Changes

If your change is big, it is HIGHLY recommended to create an issue first to discuss it.

### Guidelines
- Keep changes simple, concise, and incremental.
- Please keep the code style consistent with the rest of the codebase.

### Pull Request Process
1. Create a feature branch from `master`
2. Make your changes
3. Ensure tests pass and code is formatted
4. Push your branch and create a pull request
5. Describe what your changes do and why
6. We've github actions to run tests and linting, if they fail because of your changes, please fix them before requesting a review.

### Commit Messages
Write clear, concise commit messages describing what changed. Try to keep commits as atomic as possible.

## Questions?
If anything is unclear, ask first before making assumptions. We prefer simple, targeted contributions that solve specific problems.
