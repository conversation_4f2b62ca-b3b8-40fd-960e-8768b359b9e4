# 🔧 MXGo Outlook邮箱集成配置指南

本文档详细介绍如何配置MXGo项目以支持Outlook邮箱和硅基流动AI模型服务。

## 📋 目录

- [前置要求](#前置要求)
- [Microsoft Graph API配置](#microsoft-graph-api配置)
- [硅基流动API配置](#硅基流动api配置)
- [环境变量配置](#环境变量配置)
- [部署步骤](#部署步骤)
- [测试验证](#测试验证)
- [故障排除](#故障排除)

## 🔧 前置要求

### 系统要求
- Python 3.12+
- Docker Desktop (推荐)
- PostgreSQL 数据库
- Redis 缓存
- RabbitMQ 消息队列

### 账户要求
1. **Microsoft Azure账户** - 用于创建应用注册
2. **Outlook邮箱账户** - 至少一个Outlook/Hotmail邮箱
3. **硅基流动账户** - 用于AI模型API访问

## 🔐 Microsoft Graph API配置

### 步骤1：创建Azure应用注册

1. 登录 [Azure Portal](https://portal.azure.com/)
2. 导航到 **Azure Active Directory** > **应用注册**
3. 点击 **新注册**
4. 填写应用信息：
   - **名称**: `MXGo Email Assistant`
   - **支持的账户类型**: 选择 "任何组织目录(任何 Azure AD 目录 - 多租户)中的账户和个人 Microsoft 账户"
   - **重定向URI**: 暂时留空，稍后配置

### 步骤2：配置API权限

1. 在应用注册页面，点击 **API权限**
2. 点击 **添加权限** > **Microsoft Graph** > **应用程序权限**
3. 添加以下权限：
   ```
   Mail.Read
   Mail.Send
   Mail.ReadWrite
   User.Read
   ```
4. 点击 **授予管理员同意**

### 步骤3：创建客户端密钥

1. 点击 **证书和密钥**
2. 点击 **新客户端密钥**
3. 设置描述和过期时间
4. **重要**: 复制生成的密钥值，这是唯一显示的机会

### 步骤4：获取应用信息

记录以下信息：
- **应用程序(客户端) ID**: 在概述页面找到
- **目录(租户) ID**: 在概述页面找到
- **客户端密钥**: 刚才创建的密钥值

## 🤖 硅基流动API配置

### 步骤1：注册硅基流动账户

1. 访问 [硅基流动官网](https://siliconflow.cn/)
2. 注册账户并完成实名认证
3. 充值账户余额（建议先充值少量测试）

### 步骤2：获取API密钥

1. 登录硅基流动控制台
2. 导航到 **API密钥** 页面
3. 创建新的API密钥
4. 复制API密钥值

### 步骤3：选择模型

当前配置支持以下硅基流动模型：
- `Qwen/Qwen2.5-72B-Instruct` (主要模型)
- `meta-llama/Meta-Llama-3.1-70B-Instruct` (备用模型)
- `deepseek-ai/DeepSeek-V2.5` (推理模型)
- `Qwen/Qwen2.5-32B-Instruct` (快速模型)
- `Qwen/Qwen2.5-7B-Instruct` (轻量模型)

## ⚙️ 环境变量配置

### 步骤1：复制环境变量模板

```bash
cp .env.example .env
```

### 步骤2：配置邮件服务

在 `.env` 文件中设置：

```bash
# 邮件服务提供商选择
EMAIL_PROVIDER=outlook

# Microsoft Graph API配置
MICROSOFT_CLIENT_ID=your_microsoft_client_id_here
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret_here
MICROSOFT_TENANT_ID=common
OUTLOOK_SELECTED_MAILBOX=<EMAIL>
```

### 步骤3：配置AI模型服务

```bash
# 硅基流动API配置
SILICONFLOW_API_KEY=your_siliconflow_api_key_here

# LiteLLM配置
LITELLM_CONFIG_PATH=model.config.toml
LITELLM_DEFAULT_MODEL_GROUP=gpt-4
```

### 步骤4：配置其他必要服务

```bash
# 安全配置
X_API_KEY=your_secure_random_key_here
JWT_SECRET=your_jwt_secret_key_here

# 数据库配置
DB_HOST=postgres
DB_PORT=5432
DB_NAME=mxgo
DB_USER=mxgo
DB_PASSWORD=123456

# 消息队列配置
RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672
RABBITMQ_USER=mxgo_user
RABBITMQ_PASSWORD=123456

# 缓存配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=123456
```

## 🚀 部署步骤

### 方法1：Docker部署（推荐）

1. **安装依赖**：
   ```bash
   # 安装新的Python依赖
   pip install msal msgraph-core
   ```

2. **启动服务**：
   ```bash
   # 使用Docker Compose启动所有服务
   ./scripts/setup-local.sh && ./scripts/start-local.sh
   ```

3. **验证服务**：
   ```bash
   # 检查服务状态
   docker compose ps
   
   # 查看API服务日志
   docker compose logs api_server
   ```

### 方法2：本地开发部署

1. **安装依赖**：
   ```bash
   poetry install
   poetry shell
   ```

2. **启动基础服务**：
   ```bash
   # 启动PostgreSQL, Redis, RabbitMQ
   brew services start postgresql@16
   brew services start redis
   brew services start rabbitmq
   ```

3. **运行数据库迁移**：
   ```bash
   cd mxgo/db
   poetry run alembic upgrade head
   cd ../..
   ```

4. **启动应用服务**：
   ```bash
   # 终端1: API服务器
   poetry run python run_api.py
   
   # 终端2: 后台任务处理器
   poetry run dramatiq mxgo.tasks --watch ./
   
   # 终端3: 任务调度器
   poetry run python -m mxgo.scheduler_runner
   ```

## 🧪 测试验证

### 1. 检查邮件提供商配置

```bash
curl -X GET "http://localhost:8000/email-provider" \
  -H "x-api-key: your_api_key_here"
```

预期响应：
```json
{
  "status": "success",
  "provider": "outlook",
  "is_outlook": true,
  "is_aws_ses": false
}
```

### 2. 获取Outlook邮箱列表

```bash
curl -X GET "http://localhost:8000/outlook/mailboxes" \
  -H "x-api-key: your_api_key_here"
```

### 3. 选择邮箱

```bash
curl -X POST "http://localhost:8000/outlook/select-mailbox" \
  -H "x-api-key: your_api_key_here" \
  -F "mailbox_email=<EMAIL>"
```

### 4. 测试邮件处理

```bash
curl -X POST "http://localhost:8000/process-email" \
  -H "Content-Type: application/json" \
  -H "x-api-key: your_api_key_here" \
  -d '{
    "from_email": "<EMAIL>",
    "to": "<EMAIL>",
    "subject": "测试邮件处理",
    "textContent": "请帮我总结一下人工智能的发展历程",
    "date": "2024-01-01T10:00:00Z"
  }'
```

### 5. 运行单元测试

```bash
# 运行Outlook服务测试
pytest tests/test_outlook_service.py -v

# 运行所有测试
pytest tests/ -v
```

## 🔧 故障排除

### 常见问题1：Microsoft Graph API认证失败

**错误信息**: `Authentication failed: invalid_client`

**解决方案**:
1. 检查 `MICROSOFT_CLIENT_ID` 和 `MICROSOFT_CLIENT_SECRET` 是否正确
2. 确认Azure应用的API权限已授予管理员同意
3. 检查租户ID是否正确

### 常见问题2：硅基流动API调用失败

**错误信息**: `API key invalid`

**解决方案**:
1. 检查 `SILICONFLOW_API_KEY` 是否正确
2. 确认硅基流动账户余额充足
3. 检查模型名称是否正确

### 常见问题3：邮件发送失败

**错误信息**: `Error sending email via outlook`

**解决方案**:
1. 检查选择的邮箱是否有发送权限
2. 确认Microsoft Graph API权限包含 `Mail.Send`
3. 检查网络连接和防火墙设置

### 常见问题4：Docker服务启动失败

**错误信息**: `Port already in use`

**解决方案**:
```bash
# 查找占用端口的进程
lsof -i :8000

# 修改端口配置
echo "API_PORT=8080" >> .env

# 重启服务
docker compose down && docker compose up
```

### 日志查看

```bash
# 查看API服务日志
docker compose logs -f api_server

# 查看任务处理器日志
docker compose logs -f worker

# 查看所有服务日志
docker compose logs -f
```

## 📞 技术支持

如果遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 检查环境变量配置是否正确
3. 确认所有依赖服务正常运行
4. 参考项目GitHub Issues页面

## 🔄 更新和维护

### 定期维护任务

1. **更新API密钥**：定期轮换Microsoft和硅基流动的API密钥
2. **监控使用量**：关注API调用量和费用
3. **备份配置**：定期备份环境变量和配置文件
4. **更新依赖**：定期更新Python依赖包

### 性能优化建议

1. **模型选择**：根据任务复杂度选择合适的AI模型
2. **缓存配置**：合理配置Redis缓存提高响应速度
3. **并发处理**：根据服务器性能调整worker数量
4. **监控告警**：设置服务监控和告警机制
