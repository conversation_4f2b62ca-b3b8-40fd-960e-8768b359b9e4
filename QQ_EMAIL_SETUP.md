# 📧 QQ邮箱集成配置指南

本指南详细介绍如何配置MXGo项目使用QQ邮箱作为邮件发送服务，这是**中国用户的最佳选择**。

## 🌟 为什么选择QQ邮箱？

### ✅ 优势
- **🇨🇳 中国本土服务**：无网络限制，连接稳定
- **🆓 完全免费**：无需付费，无发送限制（日常使用）
- **⚡ 配置简单**：只需QQ邮箱和授权码
- **🔒 安全可靠**：腾讯企业级安全保障
- **📱 易于管理**：可通过QQ邮箱客户端管理

### 🆚 对比其他方案
| 服务商 | 可用性 | 费用 | 配置难度 | 稳定性 |
|--------|--------|------|----------|--------|
| **QQ邮箱** | ✅ 完全可用 | 🆓 免费 | ⭐ 简单 | ⭐⭐⭐ 高 |
| AWS SES | ❌ 中国受限 | 💰 付费 | ⭐⭐⭐ 复杂 | ⭐⭐ 中等 |
| Outlook | ⚠️ 需配置 | 🆓 免费 | ⭐⭐ 中等 | ⭐⭐ 中等 |

## 🔧 配置步骤

### 步骤1：开启QQ邮箱SMTP服务

1. **登录QQ邮箱**
   - 访问 [mail.qq.com](https://mail.qq.com)
   - 使用你的QQ号和密码登录

2. **进入设置页面**
   - 点击页面右上角的"设置"
   - 选择"账户"选项卡

3. **开启SMTP服务**
   - 找到"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"
   - 点击开启"IMAP/SMTP服务"
   - 根据提示发送短信验证

4. **获取授权码**
   - 验证成功后，系统会显示16位授权码
   - **重要**：这个授权码就是你的邮箱密码，请妥善保存
   - 格式类似：`abcdefghijklmnop`（16位字符，无空格）

### 步骤2：配置环境变量

编辑项目根目录的 `.env` 文件：

```bash
# 邮件服务提供商选择
EMAIL_PROVIDER=qq_email

# QQ邮箱配置
QQ_EMAIL=<EMAIL>
QQ_EMAIL_PASSWORD=your_16_digit_auth_code
```

**配置示例**：
```bash
EMAIL_PROVIDER=qq_email
QQ_EMAIL=<EMAIL>
QQ_EMAIL_PASSWORD=abcdefghijklmnop
```

### 步骤3：重启服务

```bash
# 重启Docker服务以应用新配置
docker compose restart api_server worker
```

## 🧪 测试配置

### 1. 测试邮件服务连接

```bash
# 测试QQ邮箱SMTP连接
curl -X POST "http://localhost:8000/email-service/test" \
  -H "x-api-key: your_api_key_here"
```

**成功响应示例**：
```json
{
  "status": "success",
  "provider": "qq_email",
  "connection_test": true,
  "message": "连接测试成功"
}
```

### 2. 查看邮件服务信息

```bash
# 查看当前邮件服务配置
curl -X GET "http://localhost:8000/email-service/info" \
  -H "x-api-key: your_api_key_here"
```

**响应示例**：
```json
{
  "status": "success",
  "provider": "qq_email",
  "available_providers": ["aws_ses", "qq_email", "outlook"],
  "configuration": {
    "email": "<EMAIL>",
    "smtp_server": "smtp.qq.com",
    "smtp_port": 587,
    "requires_auth_code": true
  }
}
```

### 3. 发送测试邮件

```bash
# 发送测试邮件处理请求
curl -X POST "http://localhost:8000/process-email" \
  -H "Content-Type: application/json" \
  -H "x-api-key: your_api_key_here" \
  -d '{
    "from_email": "<EMAIL>",
    "to": "<EMAIL>",
    "subject": "测试QQ邮箱集成",
    "textContent": "请帮我测试一下QQ邮箱是否正常工作",
    "date": "2024-01-01T10:00:00Z"
  }'
```

## 🔍 故障排除

### 常见问题1：授权码错误

**错误信息**：`Authentication failed` 或 `535 Login Fail`

**解决方案**：
1. 确认授权码是16位字符，没有空格
2. 重新获取授权码：
   - 关闭SMTP服务
   - 重新开启SMTP服务
   - 获取新的授权码

### 常见问题2：SMTP连接失败

**错误信息**：`Connection refused` 或 `Timeout`

**解决方案**：
1. 检查网络连接
2. 确认防火墙设置
3. 尝试使用SSL端口（465）

### 常见问题3：邮件发送失败

**错误信息**：`Mail delivery failed`

**解决方案**：
1. 检查收件人邮箱地址是否正确
2. 确认QQ邮箱没有被限制发送
3. 检查邮件内容是否包含敏感词

### 常见问题4：日发送量限制

**问题**：QQ邮箱有日发送量限制

**解决方案**：
1. **个人QQ邮箱**：通常每日50-100封
2. **QQ企业邮箱**：更高的发送限制
3. **多邮箱轮换**：配置多个QQ邮箱账号

## 📊 性能和限制

### QQ邮箱发送限制

| 账户类型 | 日发送量 | 单次发送 | 附件大小 |
|----------|----------|----------|----------|
| 普通QQ邮箱 | 50-100封 | 100个收件人 | 50MB |
| QQ VIP邮箱 | 200-500封 | 100个收件人 | 100MB |
| 企业QQ邮箱 | 1000+封 | 500个收件人 | 100MB |

### 性能优化建议

1. **批量发送**：合并多个收件人
2. **异步处理**：利用MXGo的队列机制
3. **错误重试**：配置合适的重试策略
4. **监控告警**：监控发送成功率

## 🔄 高级配置

### 多QQ邮箱配置

如果需要更高的发送量，可以配置多个QQ邮箱：

```bash
# 主邮箱
QQ_EMAIL=<EMAIL>
QQ_EMAIL_PASSWORD=primary_auth_code

# 备用邮箱（可在代码中实现轮换逻辑）
QQ_EMAIL_BACKUP_1=<EMAIL>
QQ_EMAIL_PASSWORD_BACKUP_1=backup1_auth_code
```

### 企业邮箱配置

如果使用QQ企业邮箱：

```bash
EMAIL_PROVIDER=qq_email
QQ_EMAIL=your_name@your_company.com
QQ_EMAIL_PASSWORD=your_enterprise_auth_code
```

## 🔒 安全建议

1. **授权码安全**：
   - 不要在代码中硬编码授权码
   - 定期更换授权码
   - 使用环境变量存储

2. **邮箱安全**：
   - 开启QQ邮箱二次验证
   - 定期检查登录记录
   - 监控异常发送活动

3. **网络安全**：
   - 使用TLS加密连接
   - 配置防火墙规则
   - 限制API访问IP

## 📈 监控和维护

### 日志监控

```bash
# 查看邮件发送日志
docker compose logs worker | grep "qq_email"

# 查看错误日志
docker compose logs worker | grep "ERROR"
```

### 定期检查

- [ ] 每周检查发送成功率
- [ ] 每月检查授权码有效性
- [ ] 每季度更新授权码
- [ ] 监控QQ邮箱政策变化

## 🎉 完成！

配置完成后，你的MXGo系统将通过QQ邮箱发送AI处理后的邮件回复。这是中国用户的最佳选择，具有以下优势：

- ✅ **稳定可靠**：无网络限制，连接稳定
- ✅ **成本低廉**：完全免费使用
- ✅ **配置简单**：只需几分钟即可完成
- ✅ **易于维护**：通过QQ邮箱客户端管理

如果遇到问题，请参考故障排除部分或查看项目日志获取详细错误信息。
