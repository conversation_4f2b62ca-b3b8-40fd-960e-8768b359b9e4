# 🚀 MXGo Outlook集成快速开始指南

本指南帮助您快速配置和启动支持Outlook邮箱的MXGo AI邮件助手。

## ⚡ 5分钟快速配置

### 步骤1：获取必要的API密钥

#### Microsoft Graph API
1. 访问 [Azure Portal](https://portal.azure.com/)
2. 创建应用注册 → 获取 `Client ID` 和 `Client Secret`
3. 配置API权限：`Mail.Read`, `Mail.Send`, `Mail.ReadWrite`, `User.Read`

#### 硅基流动API
1. 访问 [硅基流动](https://siliconflow.cn/)
2. 注册账户 → 获取 `API Key`

### 步骤2：配置环境变量

复制并编辑配置文件：
```bash
cp .env.example .env
```

在 `.env` 文件中设置：
```bash
# 邮件服务配置
EMAIL_PROVIDER=outlook
MICROSOFT_CLIENT_ID=your_client_id_here
MICROSOFT_CLIENT_SECRET=your_client_secret_here
OUTLOOK_SELECTED_MAILBOX=<EMAIL>

# AI模型配置
SILICONFLOW_API_KEY=your_siliconflow_key_here

# 安全配置
X_API_KEY=your_secure_random_key_here
```

### 步骤3：启动服务

```bash
# 安装依赖
pip install msal msgraph-core

# 启动Docker服务
./scripts/setup-local.sh && ./scripts/start-local.sh
```

### 步骤4：验证配置

```bash
# 检查邮件提供商
curl -X GET "http://localhost:8000/email-provider" \
  -H "x-api-key: your_secure_random_key_here"

# 获取邮箱列表
curl -X GET "http://localhost:8000/outlook/mailboxes" \
  -H "x-api-key: your_secure_random_key_here"
```

## 📧 使用示例

### 发送邮件进行AI处理

```bash
curl -X POST "http://localhost:8000/process-email" \
  -H "Content-Type: application/json" \
  -H "x-api-key: your_secure_random_key_here" \
  -d '{
    "from_email": "<EMAIL>",
    "to": "<EMAIL>",
    "subject": "请帮我分析市场趋势",
    "textContent": "请分析一下2024年AI行业的发展趋势和投资机会",
    "date": "2024-01-01T10:00:00Z"
  }'
```

### 邮件处理模式

MXGo支持多种邮件处理模式，通过邮件地址前缀控制：

- `<EMAIL>` - 智能问答和分析
- `<EMAIL>` - 邮件内容总结
- `<EMAIL>` - 智能回复生成
- `<EMAIL>` - 深度研究分析
- `<EMAIL>` - 内容翻译

## 🔧 高级配置

### 多邮箱支持

如果您有多个Outlook邮箱，可以通过API切换：

```bash
# 选择特定邮箱
curl -X POST "http://localhost:8000/outlook/select-mailbox" \
  -H "x-api-key: your_api_key" \
  -F "mailbox_email=<EMAIL>"
```

### 模型配置优化

编辑 `model.config.toml` 调整AI模型权重：

```toml
# 主要模型 - 高质量回答
[[model]]
model_name = "gpt-4"
[model.litellm_params]
model = "siliconflow/Qwen/Qwen2.5-72B-Instruct"
base_url = "https://api.siliconflow.cn/v1"
api_key = "os.environ/SILICONFLOW_API_KEY"
weight = 10

# 快速模型 - 简单任务
[[model]]
model_name = "gpt-3.5-turbo"
[model.litellm_params]
model = "siliconflow/Qwen/Qwen2.5-7B-Instruct"
base_url = "https://api.siliconflow.cn/v1"
api_key = "os.environ/SILICONFLOW_API_KEY"
weight = 3
```

## 🐛 常见问题解决

### 问题1：认证失败
```
错误：Authentication failed: invalid_client
解决：检查Azure应用的Client ID和Secret是否正确
```

### 问题2：API调用失败
```
错误：API key invalid
解决：检查硅基流动API密钥是否正确，账户余额是否充足
```

### 问题3：端口占用
```
错误：Port already in use
解决：修改.env中的API_PORT=8080，或停止占用进程
```

### 问题4：邮件发送失败
```
错误：Error sending email via outlook
解决：确认Microsoft Graph API权限已正确配置并授权
```

## 📊 监控和日志

### 查看服务状态
```bash
# 检查Docker服务
docker compose ps

# 查看API日志
docker compose logs -f api_server

# 查看任务处理日志
docker compose logs -f worker
```

### 健康检查
```bash
# API健康检查
curl http://localhost:8000/health

# 服务状态检查
curl -X GET "http://localhost:8000/email-provider" \
  -H "x-api-key: your_api_key"
```

## 🔒 安全建议

1. **API密钥安全**：
   - 使用强随机密钥
   - 定期轮换密钥
   - 不要在代码中硬编码密钥

2. **网络安全**：
   - 使用HTTPS（生产环境）
   - 配置防火墙规则
   - 限制API访问IP

3. **权限控制**：
   - 最小权限原则
   - 定期审查API权限
   - 监控异常访问

## 📈 性能优化

### 1. 模型选择策略
- 简单任务使用7B模型（快速、便宜）
- 复杂分析使用72B模型（高质量）
- 根据响应时间要求调整权重

### 2. 缓存优化
```bash
# 增加Redis内存
REDIS_MAXMEMORY=512mb

# 配置缓存策略
REDIS_MAXMEMORY_POLICY=allkeys-lru
```

### 3. 并发处理
```bash
# 增加worker数量
docker compose up --scale worker=3
```

## 🔄 更新和维护

### 定期维护检查清单

- [ ] 检查API密钥有效性
- [ ] 监控API调用量和费用
- [ ] 更新依赖包版本
- [ ] 备份配置文件
- [ ] 检查日志文件大小
- [ ] 验证邮件发送功能

### 更新步骤
```bash
# 拉取最新代码
git pull origin main

# 更新依赖
pip install -r requirements.txt

# 重启服务
docker compose restart
```

## 📞 获取帮助

- **文档**: 查看 `OUTLOOK_SETUP.md` 获取详细配置说明
- **测试**: 运行 `pytest tests/test_outlook_service.py` 验证功能
- **日志**: 查看 `docker compose logs` 获取错误信息
- **社区**: 访问项目GitHub页面提交Issue

## 🎉 成功！

如果您能看到以下响应，说明配置成功：

```json
{
  "status": "success",
  "provider": "outlook",
  "is_outlook": true
}
```

现在您可以开始使用MXGo的AI邮件处理功能了！发送邮件到配置的地址，系统将自动使用硅基流动的AI模型进行智能处理并通过Outlook回复。
