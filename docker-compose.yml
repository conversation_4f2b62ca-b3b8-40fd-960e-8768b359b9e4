services:
  postgres:
    image: postgres:16-alpine
    container_name: postgres
    environment:
      POSTGRES_DB: ${DB_NAME:-mxgo}
      POSTGRES_USER: ${DB_USER:-mxgo}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-docker_changeme_123}
    ports:
      - "${DB_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/init-db.sh:/docker-entrypoint-initdb.d/init-db.sh
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-mxgo} -d ${DB_NAME:-mxgo}"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: redis
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: ["redis-server", "--requirepass", "${REDIS_PASSWORD:-docker_redis_123}"]
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  rabbitmq:
    image: rabbitmq:3-management
    container_name: rabbitmq
    ports:
      - "${RABBITMQ_PORT:-5672}:5672"
      - "${RABBITMQ_MANAGEMENT_PORT:-15672}:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER:-docker_guest}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD:-docker_guest_123}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  api_server:
    build:
      context: .
      dockerfile: docker/api_server.dockerfile
    container_name: api_server
    env_file:
      - .env
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
    ports:
      - "${API_PORT:-8000}:8000"
    volumes:
      - ./model.config.toml:/app/model.config.toml:ro
      - ./attachments:/app/attachments
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  worker:
    build:
      context: .
      dockerfile: docker/worker.dockerfile
    container_name: worker
    env_file:
      - .env
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./model.config.toml:/app/model.config.toml:ro
      - ./attachments:/app/attachments
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import psutil; exit(0 if any('dramatiq' in ' '.join(p.info['cmdline'] or []) for p in psutil.process_iter(['cmdline'])) else 1)"]
      interval: 30s
      timeout: 10s
      retries: 3

  scheduler:
    build:
      context: .
      dockerfile: docker/scheduler.dockerfile
    container_name: scheduler
    env_file:
      - .env
    depends_on:
      worker:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import psutil; exit(0 if any('scheduler_runner' in ' '.join(p.info['cmdline'] or []) for p in psutil.process_iter(['cmdline'])) else 1)"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  rabbitmq_data:
  redis_data:

networks:
  default:
    name: mxgo_network
