---
sidebar_position: 3
---

# Usage

Using MXGo is straightforward. Simply forward an email with instructions to `<EMAIL>` or to one of our specialized email handles.

Here's a comprehensive overview of all available handles and their primary functions:

## 🎯 General Purpose Handle

*   **`<EMAIL>`**: Ask the agent to do anything when sending/forwarding the email, including all the functionalities mentioned below. This is the most versatile handle that can perform any task.

## 📝 Content Processing Handles

*   **`<EMAIL>`**: Get a concise summary of the email content. Perfect for long emails or documents.
*   **`<EMAIL>`**: Receive a simplified version of complex text. Great for breaking down technical content into easy-to-understand language.
*   **`<EMAIL>`**: Translate the email content to any language. Just specify the target language in your request.

## 🔍 Research & Analysis Handles

*   **`<EMAIL>`**: Perform in-depth research based on the email content [Experimental]. Provides comprehensive analysis with external sources.
*   **`<EMAIL>`**: Verify the claims made in the email. Checks facts against reliable sources.
*   **`<EMAIL>`**: Get background information on entities, people, or topics mentioned in the email.
*   **`<EMAIL>`**: Get current news and breaking stories related to topics in the email. Provides structured analysis with grouped themes.
    - **Aliases**: `<EMAIL>`, `<EMAIL>`, `<EMAIL>`, `<EMAIL>`

## 🗓️ Scheduling & Task Management Handles

*   **`<EMAIL>`**: Schedule future or recurring tasks, set reminders, or create delayed processing requests.
    - **Aliases**: `<EMAIL>`, `<EMAIL>`
    - **Example uses**: "Remind me about this next week", "Schedule a weekly summary", "Set up recurring reports"
*   **`<EMAIL>`**: Extract scheduling information, propose meeting times, or book appointments.
    - **Aliases**: `<EMAIL>`, `<EMAIL>`, `<EMAIL>`
*   **`<EMAIL>`**: Cancel or delete scheduled tasks and reminders.
    - **Aliases**: `<EMAIL>`, `<EMAIL>`

## 📄 Document & Export Handles

*   **`<EMAIL>`**: Export email content as a professional PDF document.
    - **Aliases**: `<EMAIL>`, `<EMAIL>`, `<EMAIL>`

## 🎨 Handle Features

### Attachment Support
All handles can process various types of attachments:
- **Images**: Visual analysis and description
- **PDFs**: Content extraction and analysis
- **Documents**: Text extraction and processing
- **Spreadsheets**: Data analysis and insights

### Advanced Capabilities
- **Multi-language support**: All handles work with content in multiple languages
- **Contextual understanding**: Maintains context across email threads
- **Rich formatting**: Responses include proper formatting and styling
- **Citation support**: Research handles provide source citations

## 💡 Usage Tips

**Pro tip:** Add our email handles to your contacts for quicker access when forwarding emails.

**Flexibility:** Any handle can export content as PDF by simply asking "convert to PDF" or "export as PDF" in your request.

**Scheduling:** Use natural language for scheduling - "every Monday", "in 2 weeks", "monthly on the 15th" all work perfectly.

**Attachments:** Forward emails with attachments for comprehensive analysis - images, documents, and files are all processed intelligently.

## 🔗 Self-Hosting

If you're self-hosting MXGo, replace `mxgo.ai` with your own domain in all the handles above. For example: `<EMAIL>`, `<EMAIL>`, etc.

For detailed self-hosting instructions, see our [Self-Hosting Guide](./self-hosting).
