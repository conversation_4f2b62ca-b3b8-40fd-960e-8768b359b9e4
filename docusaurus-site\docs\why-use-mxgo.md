# MXGo Use Cases

⚡ **The Magic: Zero Setup Required**

- No new apps to learn - works with your existing email
- No account creation - just whitelist your email and start forwarding
- No data stored - your emails are processed and immediately deleted
- Works anywhere - Gmail, Outlook, Apple Mail, even your phone
- Works with attachments - PDFs, images, spreadsheets all processed
- Works with email threads - understands context from conversation history
- **PDF Export** - any response can be converted to PDF, or use pdf@ to export content directly

As simple as it sounds, MXGo is very powerful. If you spend more than 10 minutes a day reading, understanding, or responding to emails, MXGo can probably save you time and mental energy. Whether you're dealing with work emails, personal correspondence, or staying informed, there's likely a use case that fits your daily routine. Plus, any content generated can be exported as a professional PDF for sharing, printing, or archiving. Here are some use-cases you can use it for.

## 📧 Daily Email Management

**"I want to set up recurring reminders or automate periodic tasks"**
- Forward your <NAME_EMAIL> (e.g., "Remind me every Monday at 9am to review sales report")
- Get confirmation and automatic reminders/results at the scheduled time

**"I get too many newsletters and updates"**
- Forward <NAME_EMAIL> - get key points in 2-3 sentences
- Set up auto-forwarding rules so all newsletters get auto-summarized daily

**"I receive emails in different languages"**
- <NAME_EMAIL> - instantly get English translations
- Perfect for international business or foreign language newsletters

**"Complex emails confuse me"**
- Forward technical/legal <NAME_EMAIL> - get plain English explanations
- Great for insurance documents, contracts, or technical announcements

**"I'm drowning in promotional emails"**
- Forward deals/<NAME_EMAIL> with "Is this actually a good deal?"
- Get honest comparisons and price checks

## 🤝 Meeting & Scheduling

**"Coordinating meetings is a nightmare"**
- Forward scheduling <NAME_EMAIL>
- Get back .ics calendar files with suggested meeting times
- Works with "Can we meet next week?" or complex group scheduling

**"I need to prepare for meetings with new people"**
- Forward meeting <NAME_EMAIL>
- Get LinkedIn profiles, company info, recent news about attendees

**"I forgot what we discussed in previous emails"**
- Forward email <NAME_EMAIL>
- Get context and key points before the meeting

**"I need to follow up after meetings"**
- Forward meeting <NAME_EMAIL> with "Draft follow-up email"
- Get professional action items and next steps

## 💼 Business & Work

**"I get invoices and need to check calculations"**
- Forward <NAME_EMAIL> with "Check if this math is correct"
- Catches billing errors automatically

**"I need to research companies/people quickly"**
- Forward emails mentioning <NAME_EMAIL>
- Get company size, recent news, key executives, competitors

**"I receive reports and need quick insights"**
- Forward quarterly <NAME_EMAIL>
- Get key metrics and trends without reading 20 pages

**"I receive contracts and legal documents"**
- <NAME_EMAIL> - understand key terms and obligations
- <NAME_EMAIL> with "What are the risks here?"

**"I get RFPs and proposals to review"**
- <NAME_EMAIL> - get key requirements and deadlines
- <NAME_EMAIL> with "What's our competitive advantage here?"

**"I need to document compliance or regulatory communications"**
- Forward regulatory <NAME_EMAIL> for permanent, clean records
- Perfect for audit trails and compliance documentation

## 🔍 Information Verification & Research

**"I get forwarded news and want to check if it's true"**
- Forward suspicious <NAME_EMAIL>
- Get verification with sources - perfect for avoiding misinformation

**"I need to verify claims in marketing emails"**
- Forward promotional <NAME_EMAIL>
- Check if "50% off" is really a discount or if testimonials are real

**"I want to research investments or financial advice"**
- Forward investment <NAME_EMAIL>
- Get company fundamentals, recent performance, analyst opinions

**"I need to check competitor information"**
- Forward competitor <NAME_EMAIL>
- Get market position, recent moves, customer feedback

## 📄 PDF Export & Document Creation

**"I need to create professional documents from email content"**
- Forward <NAME_EMAIL> - get clean, formatted PDF without email headers
- Perfect for sharing research, reports, or important communications

**"I want to convert AI responses to shareable documents"**
- Ask any handle to "generate a report and convert to PDF"
- <NAME_EMAIL> with "Create a HN newsletter with top AI posts of the week and export as PDF"

**"I need to archive important email content professionally"**
- Forward contracts, agreements, or important <NAME_EMAIL>
- Get clean documents without email clutter - perfect for filing or legal records


## 🏠 Personal Life

**"I get complex medical/insurance emails"**
- <NAME_EMAIL> - understand your benefits, procedures, costs
- No more confusion about medical bills or insurance claims

**"I get travel confirmations and want to know more about destinations"**
- Forward booking <NAME_EMAIL>
- Get weather, local attractions, cultural tips

**"I receive home service estimates"**
- Forward contractor <NAME_EMAIL> with "Is this price fair?"
- Get market rates and red flags to watch for

**"I get tax documents and financial statements"**
- <NAME_EMAIL> - understand what you owe/are owed
- <NAME_EMAIL> with "What deductions am I missing?"

**"I receive HOA notices and community updates"**
- Forward lengthy community <NAME_EMAIL>
- Get key dates, rule changes, and action items

## 🎓 Education & Learning

**"I want to stay informed but don't have time to read everything"**
- Forward industry <NAME_EMAIL>
- Set up daily digest of key news in your field

**"I receive academic papers or technical articles"**
- <NAME_EMAIL> - get key findings in simple terms
- Perfect for staying current without deep technical reading

**"I get course announcements and need to prioritize"**
- Forward educational <NAME_EMAIL> with "Which courses are worth my time?"
- Get recommendations based on your goals

**"I receive research papers and need quick insights"**
- <NAME_EMAIL> with "What are the practical applications?"
- Understand real-world relevance quickly

**"I want to compile research findings into shareable documents"**
- Forward multiple research <NAME_EMAIL> with "Synthesize findings and create PDF report"
- Perfect for thesis research or professional development

## 🛍️ Shopping & Consumer Decisions

**"I get overwhelmed by product comparison emails"**
- <NAME_EMAIL> - get clear feature comparisons
- Perfect for software, cars, appliances

**"I receive warranty and service emails"**
- <NAME_EMAIL> with "What does this actually cover?"
- Understand terms without legal jargon

**"I get subscription renewal notices"**
- <NAME_EMAIL> with "Is this still worth it?"
- Get usage analysis and alternatives

**"I receive recall notices and safety alerts"**
- <NAME_EMAIL> with "How urgent is this?"
- Understand if immediate action is needed

## 💰 Financial Management

**"I get bank statements and credit card bills"**
- <NAME_EMAIL> with "Any unusual charges or patterns?"
- Spot fraud and track spending trends

**"I receive investment updates and market reports"**
- <NAME_EMAIL> - understand impact on your portfolio
- Get action recommendations in plain English

**"I get loan offers and financial product emails"**
- <NAME_EMAIL> with "Is this a good deal for my situation?"
- Get personalized pros/cons analysis

**"I receive tax-related emails from accountants"**
- <NAME_EMAIL> - understand deadlines and requirements
- No more confusion about filing obligations

**"I receive wellness newsletters with conflicting advice"**
- <NAME_EMAIL> - verify health claims
- Separate science from marketing

## 🏥 Healthcare & Wellness

**"I get lab results and medical reports"**
- <NAME_EMAIL> - understand what results mean
- Get context on normal ranges and next steps

**"I receive appointment confirmations with prep instructions"**
- <NAME_EMAIL> - get clear to-do lists
- Never miss pre-appointment requirements

**"I get emails about new treatments or procedures"**
- <NAME_EMAIL> with "What are the risks and benefits?"
- Make informed healthcare decisions

## 🎯 Real-World Examples by Profession

**Small Business Owner:**
- "I forward customer <NAME_EMAIL> with 'Draft empathetic response' - turns angry customers into loyal ones"
- "I forward supplier <NAME_EMAIL> before negotiations - know their financial health"

**Job Seeker:**
- "I forward job <NAME_EMAIL> to research company culture before applying"
- "I forward interview <NAME_EMAIL> with 'Help me research this role' for better preparation"

**Investor:**
- "I forward earnings <NAME_EMAIL> for quick portfolio updates"
- "I forward analyst <NAME_EMAIL> with 'What's the contrarian view?' for balanced perspective"

**Consultant:**
- "I forward client <NAME_EMAIL> before meetings to understand industry challenges"
- "I forward <NAME_EMAIL> with 'What's the hidden agenda here?' to win more bids"
- "I forward research <NAME_EMAIL> with 'Create client presentation and export as PDF' for professional deliverables"

**Retiree:**
- "I forward Medicare <NAME_EMAIL> - healthcare is confusing enough"
- "I forward financial advisor <NAME_EMAIL> with 'Is this advice suitable for my age?'"

**Student:**
- "I forward professor <NAME_EMAIL> to catch important deadlines"
- "I forward scholarship <NAME_EMAIL> with 'Am I eligible for this?'"

**Freelancer:**
- "I forward client project <NAME_EMAIL> with 'What questions should I ask?'"
- "I forward contract <NAME_EMAIL> with 'What am I missing in this deal?'"

**Real Estate Agent:**
- "I forward property <NAME_EMAIL> for neighborhood insights"
- "I forward mortgage <NAME_EMAIL> to explain terms to clients"

**Healthcare Worker:**
- "I forward policy <NAME_EMAIL> - regulations change constantly"
- "I forward research <NAME_EMAIL> with 'How does this apply to my patients?'"

**Marketer:**
- "I forward competitor <NAME_EMAIL> with 'What is their strategy and what can we do better?'"
- "I forward campaign <NAME_EMAIL> with 'Help me brainstorm some visuals for this concept'"
