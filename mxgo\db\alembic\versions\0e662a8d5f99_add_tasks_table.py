"""
Add tasks table

Revision ID: 0e662a8d5f99
Revises:
Create Date: 2025-06-09 18:53:29.169937

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
import sqlmodel
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0e662a8d5f99"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "tasks",
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("task_id", sa.Uuid(), nullable=False),
        sa.Column("email_id", sqlmodel.sql.sqltypes.AutoString(), nullable=False),
        sa.Column("cron_expression", sqlmodel.sql.sqltypes.AutoString(), nullable=True),
        sa.Column(
            "status",
            sa.Enum("INITIALISED", "ACTIVE", "EXECUTING", "FINISHED", "DELETED", name="taskstatus"),
            nullable=False,
        ),
        sa.Column("email_request", sa.JSON(), nullable=True),
        sa.PrimaryKeyConstraint("task_id"),
    )
    op.create_index(op.f("ix_tasks_email_id"), "tasks", ["email_id"], unique=False)
    op.create_table(
        "task_runs",
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("run_id", sa.Uuid(), nullable=False),
        sa.Column("task_id", sa.Uuid(), nullable=False),
        sa.Column(
            "status",
            sa.Enum("INITIALISED", "IN_PROGRESS", "COMPLETED", "ERRORED", name="taskrunstatus"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["task_id"],
            ["tasks.task_id"],
        ),
        sa.PrimaryKeyConstraint("run_id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("task_runs")
    op.drop_index(op.f("ix_tasks_email_id"), table_name="tasks")
    op.drop_table("tasks")
    # ### end Alembic commands ###
