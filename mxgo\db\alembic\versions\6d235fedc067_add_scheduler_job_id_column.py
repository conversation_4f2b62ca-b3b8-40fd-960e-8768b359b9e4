"""
Add scheduling job id column

Revision ID: 6d235fedc067
Revises: 0e662a8d5f99
Create Date: 2025-06-10 21:16:35.085684

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
import sqlmodel
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "6d235fedc067"
down_revision: Union[str, None] = "0e662a8d5f99"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # DO NOT drop apscheduler_jobs table - it's managed by APScheduler
    # op.drop_index(op.f('ix_apscheduler_jobs_next_run_time'), table_name='apscheduler_jobs')
    # op.drop_table('apscheduler_jobs')
    op.add_column("tasks", sa.Column("scheduler_job_id", sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("tasks", "scheduler_job_id")
    # DO NOT recreate apscheduler_jobs table - it's managed by APScheduler
    # op.create_table('apscheduler_jobs',
    # sa.Column('id', sa.VARCHAR(length=191), autoincrement=False, nullable=False),
    # sa.Column('next_run_time', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    # sa.Column('job_state', postgresql.BYTEA(), autoincrement=False, nullable=False),
    # sa.PrimaryKeyConstraint('id', name=op.f('apscheduler_jobs_pkey'))
    # )
    # op.create_index(op.f('ix_apscheduler_jobs_next_run_time'), 'apscheduler_jobs', ['next_run_time'], unique=False)
    # ### end Alembic commands ###
