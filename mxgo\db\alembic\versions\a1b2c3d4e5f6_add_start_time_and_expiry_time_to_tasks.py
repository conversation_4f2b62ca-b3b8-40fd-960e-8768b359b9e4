"""
Add start_time and expiry_time to tasks

Revision ID: a1b2c3d4e5f6
Revises: 6d235fedc067
Create Date: 2024-12-15 12:00:00.000000

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "a1b2c3d4e5f6"
down_revision: Union[str, None] = "6d235fedc067"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("tasks", sa.Column("start_time", sa.DateTime(timezone=True), nullable=True))
    op.add_column("tasks", sa.Column("expiry_time", sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("tasks", "expiry_time")
    op.drop_column("tasks", "start_time")
    # ### end Alembic commands ###
