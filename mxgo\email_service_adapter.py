"""
Email service adapter to provide unified interface for different email providers.
Supports both AWS SES and Microsoft Outlook/Graph API.
"""

import os
from typing import Any, Dict, List, Optional
from enum import Enum

from dotenv import load_dotenv

from mxgo._logging import get_logger
from mxgo.email_sender import Email<PERSON>ender
from mxgo.outlook_service import OutlookService

# Load environment variables
load_dotenv()

# Initialize logger
logger = get_logger("mxgo.email_adapter")


class EmailProvider(Enum):
    """Supported email providers."""
    AWS_SES = "aws_ses"
    OUTLOOK = "outlook"


class EmailServiceAdapter:
    """
    Unified email service adapter that can work with multiple email providers.
    Provides a consistent interface regardless of the underlying email service.
    """

    def __init__(self, provider: str = None):
        """
        Initialize the email service adapter.
        
        Args:
            provider: Email provider to use ('aws_ses' or 'outlook')
        """
        self.provider = provider or os.getenv("EMAIL_PROVIDER", "aws_ses")
        self.service = None
        
        # Initialize the appropriate service
        if self.provider == EmailProvider.AWS_SES.value:
            self._init_aws_ses()
        elif self.provider == EmailProvider.OUTLOOK.value:
            self._init_outlook()
        else:
            raise ValueError(f"Unsupported email provider: {self.provider}")
        
        logger.info(f"EmailServiceAdapter initialized with provider: {self.provider}")

    def _init_aws_ses(self):
        """Initialize AWS SES service."""
        try:
            self.service = EmailSender()
            logger.info("AWS SES service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize AWS SES service: {str(e)}")
            raise

    def _init_outlook(self):
        """Initialize Outlook service."""
        try:
            self.service = OutlookService()
            logger.info("Outlook service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Outlook service: {str(e)}")
            raise

    async def authenticate(self, **kwargs) -> bool:
        """
        Authenticate with the email service.
        
        Args:
            **kwargs: Authentication parameters (varies by provider)
            
        Returns:
            True if authentication successful
        """
        if self.provider == EmailProvider.OUTLOOK.value:
            try:
                username = kwargs.get("username")
                password = kwargs.get("password")
                await self.service.get_access_token(username, password)
                return True
            except Exception as e:
                logger.error(f"Outlook authentication failed: {str(e)}")
                return False
        
        # AWS SES doesn't require separate authentication
        return True

    async def get_mailboxes(self) -> List[Dict[str, Any]]:
        """
        Get available mailboxes for the authenticated user.
        
        Returns:
            List of mailbox information
        """
        if self.provider == EmailProvider.OUTLOOK.value:
            return await self.service.get_user_mailboxes()
        else:
            # AWS SES uses configured sender email
            sender_email = os.getenv("SENDER_EMAIL", "<EMAIL>")
            return [{
                "email": sender_email,
                "displayName": "Default Sender",
                "type": "aws_ses"
            }]

    def set_selected_mailbox(self, mailbox: str):
        """
        Set the selected mailbox for operations.
        
        Args:
            mailbox: Email address of the mailbox to select
        """
        if self.provider == EmailProvider.OUTLOOK.value:
            self.service.set_selected_mailbox(mailbox)
        else:
            # For AWS SES, update the default sender email
            self.service.default_sender_email = mailbox

    async def send_email(
        self,
        to_address: str,
        subject: str,
        body_text: str,
        body_html: str = None,
        cc_addresses: List[str] = None,
        reply_to_addresses: List[str] = None,
        sender_email: str = None,
        attachments: List[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Send an email using the configured provider.
        
        Args:
            to_address: Recipient email address
            subject: Email subject
            body_text: Plain text body
            body_html: HTML body (optional)
            cc_addresses: List of CC addresses (optional)
            reply_to_addresses: List of reply-to addresses (optional)
            sender_email: Sender email address (optional)
            attachments: List of attachments (optional)
            
        Returns:
            Response from email service
        """
        try:
            if self.provider == EmailProvider.OUTLOOK.value:
                # Convert single recipient to list for Outlook
                to_addresses = [to_address] if isinstance(to_address, str) else to_address
                
                return await self.service.send_email(
                    to_addresses=to_addresses,
                    subject=subject,
                    body_text=body_text,
                    body_html=body_html,
                    cc_addresses=cc_addresses,
                    attachments=attachments,
                    from_mailbox=sender_email
                )
            else:
                # AWS SES
                response = await self.service.send_email(
                    to_address=to_address,
                    subject=subject,
                    body_text=body_text,
                    body_html=body_html,
                    cc_addresses=cc_addresses,
                    reply_to_addresses=reply_to_addresses,
                    sender_email=sender_email
                )
                
                # Normalize response format
                return {
                    "status": "success",
                    "message_id": response.get("MessageId", "unknown"),
                    "timestamp": response.get("timestamp")
                }
                
        except Exception as e:
            logger.error(f"Error sending email via {self.provider}: {str(e)}")
            return {
                "status": "error",
                "error": str(e)
            }

    async def send_reply(
        self,
        original_email: Dict[str, Any],
        reply_text: str,
        reply_html: str = None,
        attachments: List[Dict[str, Any]] = None,
        sender_email: str = None
    ) -> Dict[str, Any]:
        """
        Send a reply to an original email.
        
        Args:
            original_email: Original email data
            reply_text: Plain text reply content
            reply_html: HTML reply content (optional)
            attachments: List of attachments (optional)
            sender_email: Sender email address (optional)
            
        Returns:
            Response from email service
        """
        try:
            if self.provider == EmailProvider.OUTLOOK.value:
                # For Outlook, we need the original message ID
                message_id = original_email.get("id") or original_email.get("messageId")
                if not message_id:
                    # Fallback to regular send if no message ID
                    return await self.send_email(
                        to_address=original_email.get("from", original_email.get("from_email")),
                        subject=f"Re: {original_email.get('subject', '')}",
                        body_text=reply_text,
                        body_html=reply_html,
                        attachments=attachments,
                        sender_email=sender_email
                    )
                
                return await self.service.send_reply(
                    original_message_id=message_id,
                    reply_text=reply_text,
                    reply_html=reply_html,
                    attachments=attachments,
                    from_mailbox=sender_email
                )
            else:
                # AWS SES
                response = await self.service.send_reply(
                    original_email=original_email,
                    reply_text=reply_text,
                    reply_html=reply_html,
                    attachments=attachments
                )
                
                # Normalize response format
                return {
                    "status": "success",
                    "message_id": response.get("MessageId", "unknown"),
                    "timestamp": response.get("timestamp")
                }
                
        except Exception as e:
            logger.error(f"Error sending reply via {self.provider}: {str(e)}")
            return {
                "status": "error",
                "error": str(e)
            }

    async def get_emails(
        self,
        mailbox: str = None,
        folder: str = "inbox",
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Get emails from the specified mailbox (Outlook only).
        
        Args:
            mailbox: Mailbox email address
            folder: Folder name
            limit: Maximum number of emails
            
        Returns:
            List of email data
        """
        if self.provider == EmailProvider.OUTLOOK.value:
            return await self.service.get_emails(mailbox, folder, limit)
        else:
            # AWS SES doesn't support reading emails
            logger.warning("Email reading not supported with AWS SES provider")
            return []

    def get_provider(self) -> str:
        """
        Get the current email provider.
        
        Returns:
            Provider name
        """
        return self.provider

    def is_outlook_provider(self) -> bool:
        """
        Check if the current provider is Outlook.
        
        Returns:
            True if using Outlook provider
        """
        return self.provider == EmailProvider.OUTLOOK.value

    def is_aws_ses_provider(self) -> bool:
        """
        Check if the current provider is AWS SES.
        
        Returns:
            True if using AWS SES provider
        """
        return self.provider == EmailProvider.AWS_SES.value
