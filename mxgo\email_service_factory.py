"""
邮件服务工厂类
根据配置自动选择合适的邮件服务提供商
"""

import os
from typing import Any, Dict, List, Optional
from enum import Enum

from dotenv import load_dotenv

from mxgo._logging import get_logger
from mxgo.email_sender import EmailSender
from mxgo.qq_email_service import QQEmailService

# Load environment variables
load_dotenv()

# Initialize logger
logger = get_logger("mxgo.email_factory")


class EmailProvider(Enum):
    """支持的邮件服务提供商"""
    AWS_SES = "aws_ses"
    QQ_EMAIL = "qq_email"
    OUTLOOK = "outlook"


class EmailServiceFactory:
    """
    邮件服务工厂类
    根据环境变量自动选择和初始化邮件服务
    """

    @staticmethod
    def create_email_service():
        """
        根据配置创建邮件服务实例
        
        Returns:
            邮件服务实例
        """
        provider = os.getenv("EMAIL_PROVIDER", "aws_ses").lower()
        
        if provider == EmailProvider.QQ_EMAIL.value:
            return EmailServiceFactory._create_qq_service()
        elif provider == EmailProvider.AWS_SES.value:
            return EmailServiceFactory._create_aws_ses_service()
        elif provider == EmailProvider.OUTLOOK.value:
            # 如果有Outlook集成，在这里添加
            logger.warning("Outlook provider not yet implemented in factory")
            return EmailServiceFactory._create_aws_ses_service()  # 回退到AWS SES
        else:
            logger.warning(f"Unknown email provider: {provider}, falling back to AWS SES")
            return EmailServiceFactory._create_aws_ses_service()

    @staticmethod
    def _create_qq_service():
        """创建QQ邮箱服务"""
        try:
            service = QQEmailService()
            logger.info("QQ邮箱服务创建成功")
            return service
        except Exception as e:
            logger.error(f"QQ邮箱服务创建失败: {str(e)}")
            logger.info("回退到AWS SES服务")
            return EmailServiceFactory._create_aws_ses_service()

    @staticmethod
    def _create_aws_ses_service():
        """创建AWS SES服务"""
        try:
            service = EmailSender()
            logger.info("AWS SES服务创建成功")
            return service
        except Exception as e:
            logger.error(f"AWS SES服务创建失败: {str(e)}")
            raise

    @staticmethod
    def get_provider_info() -> Dict[str, Any]:
        """
        获取当前邮件服务提供商信息
        
        Returns:
            提供商信息
        """
        provider = os.getenv("EMAIL_PROVIDER", "aws_ses").lower()
        
        provider_info = {
            "provider": provider,
            "available_providers": [p.value for p in EmailProvider],
            "configuration": {}
        }
        
        if provider == EmailProvider.QQ_EMAIL.value:
            provider_info["configuration"] = {
                "email": os.getenv("QQ_EMAIL", "未配置"),
                "smtp_server": "smtp.qq.com",
                "smtp_port": 587,
                "requires_auth_code": True
            }
        elif provider == EmailProvider.AWS_SES.value:
            provider_info["configuration"] = {
                "region": os.getenv("AWS_REGION", "未配置"),
                "sender_email": os.getenv("SENDER_EMAIL", "未配置"),
                "requires_aws_credentials": True
            }
        
        return provider_info

    @staticmethod
    def test_email_service() -> Dict[str, Any]:
        """
        测试当前邮件服务配置
        
        Returns:
            测试结果
        """
        try:
            service = EmailServiceFactory.create_email_service()
            
            # 如果是QQ邮箱服务，测试连接
            if hasattr(service, 'test_connection'):
                connection_ok = service.test_connection()
                return {
                    "status": "success" if connection_ok else "failed",
                    "provider": os.getenv("EMAIL_PROVIDER", "aws_ses"),
                    "connection_test": connection_ok,
                    "message": "连接测试成功" if connection_ok else "连接测试失败"
                }
            else:
                # AWS SES等其他服务
                return {
                    "status": "success",
                    "provider": os.getenv("EMAIL_PROVIDER", "aws_ses"),
                    "connection_test": "not_supported",
                    "message": "服务创建成功，但不支持连接测试"
                }
                
        except Exception as e:
            return {
                "status": "error",
                "provider": os.getenv("EMAIL_PROVIDER", "aws_ses"),
                "error": str(e),
                "message": f"邮件服务测试失败: {str(e)}"
            }


class UnifiedEmailService:
    """
    统一邮件服务接口
    提供一致的邮件发送接口，无论底层使用哪种服务
    """

    def __init__(self):
        self.service = EmailServiceFactory.create_email_service()
        self.provider = os.getenv("EMAIL_PROVIDER", "aws_ses").lower()

    async def send_email(
        self,
        to_address: str,
        subject: str,
        body_text: str,
        body_html: str = None,
        cc_addresses: List[str] = None,
        attachments: List[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        统一的邮件发送接口
        
        Args:
            to_address: 收件人
            subject: 主题
            body_text: 文本内容
            body_html: HTML内容
            cc_addresses: 抄送地址
            attachments: 附件
            **kwargs: 其他参数
            
        Returns:
            发送结果
        """
        try:
            if self.provider == EmailProvider.QQ_EMAIL.value:
                return await self.service.send_email(
                    to_address=to_address,
                    subject=subject,
                    body_text=body_text,
                    body_html=body_html,
                    cc_addresses=cc_addresses,
                    attachments=attachments
                )
            else:
                # AWS SES
                return await self.service.send_email(
                    to_address=to_address,
                    subject=subject,
                    body_text=body_text,
                    body_html=body_html,
                    cc_addresses=cc_addresses,
                    **kwargs
                )
        except Exception as e:
            logger.error(f"邮件发送失败: {str(e)}")
            return {
                "status": "error",
                "error": str(e)
            }

    async def send_reply(
        self,
        original_email: Dict[str, Any],
        reply_text: str,
        reply_html: str = None,
        attachments: List[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        统一的回复邮件接口
        
        Args:
            original_email: 原始邮件
            reply_text: 回复文本
            reply_html: 回复HTML
            attachments: 附件
            
        Returns:
            发送结果
        """
        try:
            return await self.service.send_reply(
                original_email=original_email,
                reply_text=reply_text,
                reply_html=reply_html,
                attachments=attachments
            )
        except Exception as e:
            logger.error(f"回复邮件发送失败: {str(e)}")
            return {
                "status": "error",
                "error": str(e)
            }
