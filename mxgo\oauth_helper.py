"""
OAuth2 helper for Microsoft Graph API authentication.
Provides utilities for interactive authentication and token management.
"""

import os
import webbrowser
from typing import Dict, Any, Optional
from urllib.parse import urlparse, parse_qs

from dotenv import load_dotenv
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import HTMLResponse
import uvicorn

from mxgo.outlook_service import OutlookService
from mxgo._logging import get_logger

# Load environment variables
load_dotenv()

# Initialize logger
logger = get_logger("mxgo.oauth_helper")


class OAuthHelper:
    """
    Helper class for Microsoft Graph OAuth2 authentication flow.
    Provides interactive authentication for development and testing.
    """

    def __init__(self, client_id: str = None, client_secret: str = None, tenant_id: str = None):
        """
        Initialize OAuth helper.
        
        Args:
            client_id: Microsoft application client ID
            client_secret: Microsoft application client secret
            tenant_id: Microsoft tenant ID
        """
        self.client_id = client_id or os.getenv("MICROSOFT_CLIENT_ID")
        self.client_secret = client_secret or os.getenv("MICROSOFT_CLIENT_SECRET")
        self.tenant_id = tenant_id or os.getenv("MICROSOFT_TENANT_ID", "common")
        
        if not self.client_id or not self.client_secret:
            raise ValueError("Microsoft Graph API credentials are required")
        
        self.outlook_service = OutlookService(self.client_id, self.client_secret, self.tenant_id)
        self.redirect_uri = "http://localhost:8080/auth/callback"
        self.access_token = None
        
        logger.info("OAuthHelper initialized")

    def get_auth_url(self) -> str:
        """
        Get the authorization URL for OAuth2 flow.
        
        Returns:
            Authorization URL
        """
        auth_url = self.outlook_service.get_auth_url(self.redirect_uri)
        logger.info(f"Generated auth URL: {auth_url}")
        return auth_url

    async def exchange_code_for_token(self, code: str) -> str:
        """
        Exchange authorization code for access token.
        
        Args:
            code: Authorization code from callback
            
        Returns:
            Access token
        """
        try:
            token = await self.outlook_service.get_token_from_code(code, self.redirect_uri)
            self.access_token = token
            logger.info("Successfully exchanged code for access token")
            return token
        except Exception as e:
            logger.error(f"Error exchanging code for token: {str(e)}")
            raise

    def start_interactive_auth(self, port: int = 8080) -> str:
        """
        Start interactive OAuth2 authentication flow.
        Opens browser and starts local server to handle callback.
        
        Args:
            port: Port for local callback server
            
        Returns:
            Access token
        """
        # Create FastAPI app for handling callback
        app = FastAPI()
        
        @app.get("/auth/callback")
        async def auth_callback(request: Request):
            """Handle OAuth2 callback."""
            try:
                # Get authorization code from query parameters
                code = request.query_params.get("code")
                error = request.query_params.get("error")
                
                if error:
                    error_description = request.query_params.get("error_description", "Unknown error")
                    logger.error(f"OAuth error: {error} - {error_description}")
                    return HTMLResponse(f"""
                    <html>
                        <body>
                            <h1>Authentication Failed</h1>
                            <p>Error: {error}</p>
                            <p>Description: {error_description}</p>
                            <p>Please close this window and try again.</p>
                        </body>
                    </html>
                    """, status_code=400)
                
                if not code:
                    logger.error("No authorization code received")
                    return HTMLResponse("""
                    <html>
                        <body>
                            <h1>Authentication Failed</h1>
                            <p>No authorization code received.</p>
                            <p>Please close this window and try again.</p>
                        </body>
                    </html>
                    """, status_code=400)
                
                # Exchange code for token
                token = await self.exchange_code_for_token(code)
                
                return HTMLResponse(f"""
                <html>
                    <body>
                        <h1>Authentication Successful!</h1>
                        <p>Access token obtained successfully.</p>
                        <p>You can now close this window.</p>
                        <script>
                            setTimeout(function() {{
                                window.close();
                            }}, 3000);
                        </script>
                    </body>
                </html>
                """)
                
            except Exception as e:
                logger.error(f"Error in auth callback: {str(e)}")
                return HTMLResponse(f"""
                <html>
                    <body>
                        <h1>Authentication Error</h1>
                        <p>Error: {str(e)}</p>
                        <p>Please close this window and try again.</p>
                    </body>
                </html>
                """, status_code=500)
        
        # Update redirect URI with actual port
        self.redirect_uri = f"http://localhost:{port}/auth/callback"
        
        # Get authorization URL and open browser
        auth_url = self.get_auth_url()
        print(f"\n🔐 Opening browser for Microsoft authentication...")
        print(f"📱 If browser doesn't open automatically, visit: {auth_url}")
        print(f"🔄 Waiting for authentication callback on port {port}...")
        
        try:
            webbrowser.open(auth_url)
        except Exception as e:
            logger.warning(f"Could not open browser automatically: {str(e)}")
        
        # Start server and wait for callback
        try:
            uvicorn.run(app, host="localhost", port=port, log_level="warning")
        except KeyboardInterrupt:
            logger.info("Authentication cancelled by user")
            raise Exception("Authentication cancelled")
        
        return self.access_token

    async def test_token(self) -> Dict[str, Any]:
        """
        Test the current access token by making a simple API call.
        
        Returns:
            User information if token is valid
        """
        if not self.access_token:
            raise Exception("No access token available")
        
        try:
            # Test token by getting user info
            user_info = await self.outlook_service._make_graph_request("GET", "/me")
            logger.info("Access token is valid")
            return user_info
        except Exception as e:
            logger.error(f"Access token test failed: {str(e)}")
            raise

    async def get_mailboxes(self) -> list:
        """
        Get available mailboxes using current token.
        
        Returns:
            List of mailboxes
        """
        if not self.access_token:
            raise Exception("No access token available")
        
        return await self.outlook_service.get_user_mailboxes()

    def save_token_to_env(self, env_file: str = ".env") -> None:
        """
        Save access token to environment file (for development only).
        
        Args:
            env_file: Path to environment file
        """
        if not self.access_token:
            raise Exception("No access token to save")
        
        # Read existing env file
        env_lines = []
        if os.path.exists(env_file):
            with open(env_file, 'r') as f:
                env_lines = f.readlines()
        
        # Update or add token line
        token_line = f"MICROSOFT_ACCESS_TOKEN={self.access_token}\n"
        token_found = False
        
        for i, line in enumerate(env_lines):
            if line.startswith("MICROSOFT_ACCESS_TOKEN="):
                env_lines[i] = token_line
                token_found = True
                break
        
        if not token_found:
            env_lines.append(token_line)
        
        # Write back to file
        with open(env_file, 'w') as f:
            f.writelines(env_lines)
        
        logger.info(f"Access token saved to {env_file}")


def main():
    """
    Main function for interactive OAuth2 authentication.
    Run this script to authenticate with Microsoft Graph API.
    """
    try:
        print("🚀 MXGo Microsoft Graph OAuth2 Authentication Helper")
        print("=" * 60)
        
        # Initialize OAuth helper
        oauth_helper = OAuthHelper()
        
        # Start interactive authentication
        print("Starting interactive authentication...")
        token = oauth_helper.start_interactive_auth()
        
        if token:
            print(f"✅ Authentication successful!")
            print(f"🔑 Access token: {token[:50]}...")
            
            # Test the token
            print("\n🧪 Testing access token...")
            import asyncio
            user_info = asyncio.run(oauth_helper.test_token())
            print(f"👤 Authenticated as: {user_info.get('displayName')} ({user_info.get('mail')})")
            
            # Get mailboxes
            print("\n📧 Getting available mailboxes...")
            mailboxes = asyncio.run(oauth_helper.get_mailboxes())
            for mailbox in mailboxes:
                print(f"   📮 {mailbox['email']} - {mailbox['displayName']}")
            
            # Save token to .env file
            save_choice = input("\n💾 Save access token to .env file? (y/N): ").lower()
            if save_choice == 'y':
                oauth_helper.save_token_to_env()
                print("✅ Token saved to .env file")
            
            print("\n🎉 Setup complete! You can now use MXGo with Outlook integration.")
            
        else:
            print("❌ Authentication failed")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        logger.error(f"OAuth helper error: {str(e)}")


if __name__ == "__main__":
    main()
