"""
Microsoft Graph API integration for Outlook email services.
Provides OAuth2 authentication, email reading, and sending functionality.
"""

import base64
import json
import os
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional
from urllib.parse import urlencode

import httpx
import msal
from dotenv import load_dotenv

from mxgo._logging import get_logger

# Load environment variables
load_dotenv()

# Initialize logger
logger = get_logger("mxgo.outlook")

# Microsoft Graph API endpoints
GRAPH_API_BASE = "https://graph.microsoft.com/v1.0"
AUTHORITY = "https://login.microsoftonline.com/common"

# Required scopes for email operations
SCOPES = [
    "https://graph.microsoft.com/Mail.Read",
    "https://graph.microsoft.com/Mail.Send",
    "https://graph.microsoft.com/Mail.ReadWrite",
    "https://graph.microsoft.com/User.Read",
]


class OutlookService:
    """
    Service class for Microsoft Graph API integration with Outlook.
    Handles OAuth2 authentication, email reading, and sending.
    """

    def __init__(self, client_id: str = None, client_secret: str = None, tenant_id: str = None):
        """
        Initialize the Outlook service with Microsoft Graph API credentials.
        
        Args:
            client_id: Microsoft application client ID
            client_secret: Microsoft application client secret
            tenant_id: Microsoft tenant ID (optional, defaults to common)
        """
        self.client_id = client_id or os.getenv("MICROSOFT_CLIENT_ID")
        self.client_secret = client_secret or os.getenv("MICROSOFT_CLIENT_SECRET")
        self.tenant_id = tenant_id or os.getenv("MICROSOFT_TENANT_ID", "common")
        
        if not self.client_id or not self.client_secret:
            raise ValueError("Microsoft Graph API credentials are required. Please set MICROSOFT_CLIENT_ID and MICROSOFT_CLIENT_SECRET environment variables.")
        
        # Configure authority URL
        self.authority = f"https://login.microsoftonline.com/{self.tenant_id}"
        
        # Initialize MSAL app
        self.app = msal.ConfidentialClientApplication(
            client_id=self.client_id,
            client_credential=self.client_secret,
            authority=self.authority
        )
        
        self.access_token = None
        self.selected_mailbox = os.getenv("OUTLOOK_SELECTED_MAILBOX")
        
        logger.info(f"OutlookService initialized with tenant: {self.tenant_id}")

    async def get_access_token(self, username: str = None, password: str = None) -> str:
        """
        Get access token using Resource Owner Password Credentials flow or client credentials.
        
        Args:
            username: User email address (optional)
            password: User password (optional)
            
        Returns:
            Access token string
        """
        try:
            # Try client credentials flow first (for app-only access)
            result = self.app.acquire_token_for_client(scopes=SCOPES)
            
            if "access_token" in result:
                self.access_token = result["access_token"]
                logger.info("Successfully acquired access token using client credentials")
                return self.access_token
            
            # If client credentials fail and we have user credentials, try ROPC flow
            if username and password:
                result = self.app.acquire_token_by_username_password(
                    username=username,
                    password=password,
                    scopes=SCOPES
                )
                
                if "access_token" in result:
                    self.access_token = result["access_token"]
                    logger.info("Successfully acquired access token using username/password")
                    return self.access_token
            
            # If both methods fail, log the error
            error_msg = result.get("error_description", result.get("error", "Unknown error"))
            logger.error(f"Failed to acquire access token: {error_msg}")
            raise Exception(f"Authentication failed: {error_msg}")
            
        except Exception as e:
            logger.error(f"Error acquiring access token: {str(e)}")
            raise

    def get_auth_url(self, redirect_uri: str) -> str:
        """
        Get authorization URL for OAuth2 flow.
        
        Args:
            redirect_uri: Redirect URI after authorization
            
        Returns:
            Authorization URL
        """
        auth_url = self.app.get_authorization_request_url(
            scopes=SCOPES,
            redirect_uri=redirect_uri
        )
        return auth_url

    async def get_token_from_code(self, code: str, redirect_uri: str) -> str:
        """
        Exchange authorization code for access token.
        
        Args:
            code: Authorization code from OAuth2 callback
            redirect_uri: Redirect URI used in authorization request
            
        Returns:
            Access token string
        """
        try:
            result = self.app.acquire_token_by_authorization_code(
                code=code,
                scopes=SCOPES,
                redirect_uri=redirect_uri
            )
            
            if "access_token" in result:
                self.access_token = result["access_token"]
                logger.info("Successfully acquired access token from authorization code")
                return self.access_token
            else:
                error_msg = result.get("error_description", result.get("error", "Unknown error"))
                logger.error(f"Failed to acquire token from code: {error_msg}")
                raise Exception(f"Token exchange failed: {error_msg}")
                
        except Exception as e:
            logger.error(f"Error exchanging code for token: {str(e)}")
            raise

    async def _make_graph_request(self, method: str, endpoint: str, data: dict = None) -> dict:
        """
        Make authenticated request to Microsoft Graph API.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint (without base URL)
            data: Request data for POST/PUT requests
            
        Returns:
            Response data as dictionary
        """
        if not self.access_token:
            raise Exception("No access token available. Please authenticate first.")
        
        url = f"{GRAPH_API_BASE}/{endpoint.lstrip('/')}"
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        async with httpx.AsyncClient() as client:
            if method.upper() == "GET":
                response = await client.get(url, headers=headers)
            elif method.upper() == "POST":
                response = await client.post(url, headers=headers, json=data)
            elif method.upper() == "PUT":
                response = await client.put(url, headers=headers, json=data)
            elif method.upper() == "DELETE":
                response = await client.delete(url, headers=headers)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
        
        if response.status_code >= 400:
            error_msg = f"Graph API request failed: {response.status_code} - {response.text}"
            logger.error(error_msg)
            raise Exception(error_msg)
        
        return response.json() if response.content else {}

    async def get_user_mailboxes(self) -> List[Dict[str, Any]]:
        """
        Get list of user's mailboxes/email addresses.
        
        Returns:
            List of mailbox information
        """
        try:
            # Get user profile first
            user_info = await self._make_graph_request("GET", "/me")
            
            # Get mail folders to identify different mailboxes
            folders = await self._make_graph_request("GET", "/me/mailFolders")
            
            mailboxes = [{
                "email": user_info.get("mail", user_info.get("userPrincipalName")),
                "displayName": user_info.get("displayName"),
                "id": user_info.get("id"),
                "type": "primary"
            }]
            
            logger.info(f"Found {len(mailboxes)} mailboxes for user")
            return mailboxes
            
        except Exception as e:
            logger.error(f"Error getting user mailboxes: {str(e)}")
            raise

    async def get_emails(self, mailbox: str = None, folder: str = "inbox", limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get emails from specified mailbox and folder.
        
        Args:
            mailbox: Mailbox email address (uses selected mailbox if None)
            folder: Folder name (default: inbox)
            limit: Maximum number of emails to retrieve
            
        Returns:
            List of email data
        """
        try:
            # Use selected mailbox if none specified
            if not mailbox:
                mailbox = self.selected_mailbox
            
            # Build endpoint - for primary mailbox, use /me/messages
            if not mailbox or mailbox == "me":
                endpoint = f"/me/messages?$top={limit}&$orderby=receivedDateTime desc"
            else:
                endpoint = f"/users/{mailbox}/messages?$top={limit}&$orderby=receivedDateTime desc"
            
            # Add folder filter if specified
            if folder and folder.lower() != "inbox":
                endpoint += f"&$filter=parentFolderId eq '{folder}'"
            
            response = await self._make_graph_request("GET", endpoint)
            emails = response.get("value", [])
            
            logger.info(f"Retrieved {len(emails)} emails from {mailbox or 'primary mailbox'}")
            return emails
            
        except Exception as e:
            logger.error(f"Error getting emails: {str(e)}")
            raise

    async def send_email(
        self,
        to_addresses: List[str],
        subject: str,
        body_text: str,
        body_html: str = None,
        cc_addresses: List[str] = None,
        bcc_addresses: List[str] = None,
        attachments: List[Dict[str, Any]] = None,
        from_mailbox: str = None
    ) -> Dict[str, Any]:
        """
        Send an email using Microsoft Graph API.

        Args:
            to_addresses: List of recipient email addresses
            subject: Email subject
            body_text: Plain text body
            body_html: HTML body (optional)
            cc_addresses: List of CC recipients (optional)
            bcc_addresses: List of BCC recipients (optional)
            attachments: List of attachments (optional)
            from_mailbox: Sender mailbox (uses selected mailbox if None)

        Returns:
            Response from Graph API
        """
        try:
            # Build recipients list
            to_recipients = [{"emailAddress": {"address": addr}} for addr in to_addresses]
            cc_recipients = [{"emailAddress": {"address": addr}} for addr in (cc_addresses or [])]
            bcc_recipients = [{"emailAddress": {"address": addr}} for addr in (bcc_addresses or [])]

            # Build message body
            message_body = {
                "contentType": "html" if body_html else "text",
                "content": body_html or body_text
            }

            # Build message object
            message = {
                "subject": subject,
                "body": message_body,
                "toRecipients": to_recipients
            }

            if cc_recipients:
                message["ccRecipients"] = cc_recipients
            if bcc_recipients:
                message["bccRecipients"] = bcc_recipients

            # Add attachments if provided
            if attachments:
                message["attachments"] = []
                for attachment in attachments:
                    att_data = {
                        "@odata.type": "#microsoft.graph.fileAttachment",
                        "name": attachment.get("filename", "attachment"),
                        "contentType": attachment.get("mimetype", "application/octet-stream")
                    }

                    # Handle content encoding
                    content = attachment.get("content")
                    if isinstance(content, bytes):
                        att_data["contentBytes"] = base64.b64encode(content).decode()
                    elif isinstance(content, str):
                        # Assume it's already base64 encoded
                        att_data["contentBytes"] = content

                    message["attachments"].append(att_data)

            # Determine endpoint based on mailbox
            if not from_mailbox:
                from_mailbox = self.selected_mailbox

            if not from_mailbox or from_mailbox == "me":
                endpoint = "/me/sendMail"
            else:
                endpoint = f"/users/{from_mailbox}/sendMail"

            # Send the email
            request_data = {"message": message}
            response = await self._make_graph_request("POST", endpoint, request_data)

            logger.info(f"Email sent successfully to {', '.join(to_addresses)}")
            return {
                "status": "success",
                "message_id": response.get("id", "unknown"),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error(f"Error sending email: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

    async def send_reply(
        self,
        original_message_id: str,
        reply_text: str,
        reply_html: str = None,
        attachments: List[Dict[str, Any]] = None,
        from_mailbox: str = None
    ) -> Dict[str, Any]:
        """
        Send a reply to an existing email.

        Args:
            original_message_id: ID of the original message to reply to
            reply_text: Plain text reply content
            reply_html: HTML reply content (optional)
            attachments: List of attachments (optional)
            from_mailbox: Sender mailbox (uses selected mailbox if None)

        Returns:
            Response from Graph API
        """
        try:
            # Build reply message body
            message_body = {
                "contentType": "html" if reply_html else "text",
                "content": reply_html or reply_text
            }

            # Build reply object
            reply_data = {
                "message": {
                    "body": message_body
                }
            }

            # Add attachments if provided
            if attachments:
                reply_data["message"]["attachments"] = []
                for attachment in attachments:
                    att_data = {
                        "@odata.type": "#microsoft.graph.fileAttachment",
                        "name": attachment.get("filename", "attachment"),
                        "contentType": attachment.get("mimetype", "application/octet-stream")
                    }

                    # Handle content encoding
                    content = attachment.get("content")
                    if isinstance(content, bytes):
                        att_data["contentBytes"] = base64.b64encode(content).decode()
                    elif isinstance(content, str):
                        att_data["contentBytes"] = content

                    reply_data["message"]["attachments"].append(att_data)

            # Determine endpoint based on mailbox
            if not from_mailbox:
                from_mailbox = self.selected_mailbox

            if not from_mailbox or from_mailbox == "me":
                endpoint = f"/me/messages/{original_message_id}/reply"
            else:
                endpoint = f"/users/{from_mailbox}/messages/{original_message_id}/reply"

            # Send the reply
            response = await self._make_graph_request("POST", endpoint, reply_data)

            logger.info(f"Reply sent successfully for message {original_message_id}")
            return {
                "status": "success",
                "message_id": response.get("id", "unknown"),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error(f"Error sending reply: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

    def set_selected_mailbox(self, mailbox: str):
        """
        Set the selected mailbox for operations.

        Args:
            mailbox: Email address of the mailbox to select
        """
        self.selected_mailbox = mailbox
        logger.info(f"Selected mailbox set to: {mailbox}")

    async def get_message_details(self, message_id: str, mailbox: str = None) -> Dict[str, Any]:
        """
        Get detailed information about a specific message.

        Args:
            message_id: ID of the message
            mailbox: Mailbox to search in (uses selected mailbox if None)

        Returns:
            Message details
        """
        try:
            if not mailbox:
                mailbox = self.selected_mailbox

            if not mailbox or mailbox == "me":
                endpoint = f"/me/messages/{message_id}"
            else:
                endpoint = f"/users/{mailbox}/messages/{message_id}"

            message = await self._make_graph_request("GET", endpoint)
            logger.info(f"Retrieved message details for {message_id}")
            return message

        except Exception as e:
            logger.error(f"Error getting message details: {str(e)}")
            raise
