"""
QQ邮箱SMTP服务集成
支持通过QQ邮箱发送邮件回复
"""

import os
import smtplib
from email.mime.application import MIMEApplication
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.utils import formataddr
from typing import Any, Dict, List, Optional
from datetime import datetime, timezone

from dotenv import load_dotenv

from mxgo._logging import get_logger

# Load environment variables
load_dotenv()

# Initialize logger
logger = get_logger("mxgo.qq_email")


class QQEmailService:
    """
    QQ邮箱SMTP服务类
    支持通过QQ邮箱发送邮件和回复
    """

    def __init__(self, email: str = None, password: str = None):
        """
        初始化QQ邮箱服务
        
        Args:
            email: QQ邮箱地址
            password: QQ邮箱授权码（不是QQ密码）
        """
        self.email = email or os.getenv("QQ_EMAIL")
        self.password = password or os.getenv("QQ_EMAIL_PASSWORD")
        
        if not self.email or not self.password:
            raise ValueError("QQ邮箱地址和授权码是必需的。请设置QQ_EMAIL和QQ_EMAIL_PASSWORD环境变量。")
        
        # QQ邮箱SMTP配置
        self.smtp_server = "smtp.qq.com"
        self.smtp_port = 587  # 使用TLS
        self.smtp_ssl_port = 465  # 使用SSL
        
        logger.info(f"QQ邮箱服务初始化完成: {self.email}")

    def _create_smtp_connection(self, use_ssl: bool = False) -> smtplib.SMTP:
        """
        创建SMTP连接
        
        Args:
            use_ssl: 是否使用SSL连接
            
        Returns:
            SMTP连接对象
        """
        try:
            if use_ssl:
                server = smtplib.SMTP_SSL(self.smtp_server, self.smtp_ssl_port)
            else:
                server = smtplib.SMTP(self.smtp_server, self.smtp_port)
                server.starttls()  # 启用TLS加密
            
            server.login(self.email, self.password)
            logger.info(f"SMTP连接成功: {self.smtp_server}")
            return server
            
        except Exception as e:
            logger.error(f"SMTP连接失败: {str(e)}")
            raise

    async def send_email(
        self,
        to_address: str,
        subject: str,
        body_text: str,
        body_html: str = None,
        cc_addresses: List[str] = None,
        attachments: List[Dict[str, Any]] = None,
        sender_name: str = "MXGo AI助手"
    ) -> Dict[str, Any]:
        """
        发送邮件
        
        Args:
            to_address: 收件人邮箱
            subject: 邮件主题
            body_text: 纯文本内容
            body_html: HTML内容（可选）
            cc_addresses: 抄送地址列表（可选）
            attachments: 附件列表（可选）
            sender_name: 发件人显示名称
            
        Returns:
            发送结果
        """
        try:
            # 创建邮件对象
            msg = MIMEMultipart('alternative')
            msg['From'] = formataddr((sender_name, self.email))
            msg['To'] = to_address
            msg['Subject'] = subject
            
            if cc_addresses:
                msg['Cc'] = ', '.join(cc_addresses)
            
            # 添加邮件内容
            if body_text:
                text_part = MIMEText(body_text, 'plain', 'utf-8')
                msg.attach(text_part)
            
            if body_html:
                html_part = MIMEText(body_html, 'html', 'utf-8')
                msg.attach(html_part)
            
            # 添加附件
            if attachments:
                for attachment in attachments:
                    try:
                        filename = attachment.get('filename', 'attachment')
                        content = attachment.get('content')
                        mimetype = attachment.get('mimetype', 'application/octet-stream')
                        
                        if isinstance(content, str):
                            # 假设是base64编码的内容
                            import base64
                            content = base64.b64decode(content)
                        
                        att = MIMEApplication(content, _subtype=mimetype.split('/')[-1])
                        att.add_header('Content-Disposition', 'attachment', filename=filename)
                        msg.attach(att)
                        
                    except Exception as e:
                        logger.error(f"添加附件失败 {filename}: {str(e)}")
            
            # 发送邮件
            recipients = [to_address]
            if cc_addresses:
                recipients.extend(cc_addresses)
            
            server = self._create_smtp_connection()
            server.send_message(msg, to_addrs=recipients)
            server.quit()
            
            logger.info(f"邮件发送成功: {to_address}")
            
            return {
                "status": "success",
                "message_id": f"qq-{int(datetime.now().timestamp())}",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "to": to_address,
                "subject": subject
            }
            
        except Exception as e:
            logger.error(f"邮件发送失败: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

    async def send_reply(
        self,
        original_email: Dict[str, Any],
        reply_text: str,
        reply_html: str = None,
        attachments: List[Dict[str, Any]] = None,
        sender_name: str = "MXGo AI助手"
    ) -> Dict[str, Any]:
        """
        发送回复邮件
        
        Args:
            original_email: 原始邮件信息
            reply_text: 回复文本内容
            reply_html: 回复HTML内容（可选）
            attachments: 附件列表（可选）
            sender_name: 发件人显示名称
            
        Returns:
            发送结果
        """
        try:
            # 提取原始邮件信息
            original_from = original_email.get('from_email', original_email.get('from'))
            original_subject = original_email.get('subject', '')
            
            # 构造回复主题
            reply_subject = original_subject
            if not reply_subject.lower().startswith('re:'):
                reply_subject = f"Re: {reply_subject}"
            
            # 发送回复
            return await self.send_email(
                to_address=original_from,
                subject=reply_subject,
                body_text=reply_text,
                body_html=reply_html,
                attachments=attachments,
                sender_name=sender_name
            )
            
        except Exception as e:
            logger.error(f"发送回复失败: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

    def test_connection(self) -> bool:
        """
        测试SMTP连接
        
        Returns:
            连接是否成功
        """
        try:
            server = self._create_smtp_connection()
            server.quit()
            logger.info("QQ邮箱SMTP连接测试成功")
            return True
        except Exception as e:
            logger.error(f"QQ邮箱SMTP连接测试失败: {str(e)}")
            return False

    @staticmethod
    def get_setup_instructions() -> str:
        """
        获取QQ邮箱设置说明
        
        Returns:
            设置说明文本
        """
        return """
QQ邮箱SMTP设置说明：

1. 登录QQ邮箱 (mail.qq.com)
2. 点击"设置" -> "账户"
3. 找到"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"
4. 开启"IMAP/SMTP服务"
5. 按照提示发送短信验证
6. 获取授权码（16位字符，如：abcdefghijklmnop）
7. 在.env文件中配置：
   QQ_EMAIL=<EMAIL>
   QQ_EMAIL_PASSWORD=your_16_digit_auth_code

注意：
- QQ_EMAIL_PASSWORD是授权码，不是QQ密码
- 授权码通常是16位字符，没有空格
- 确保QQ邮箱已开启SMTP服务
        """
