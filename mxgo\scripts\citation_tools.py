"""
Citation-aware wrapper tools for the Email Deep Research Agent.

This module provides wrapper classes that extend the original tools but collect URL
information for a simple references section without embedding citations in text.
"""

import re
import time

from smolagents import GoogleSearchTool

from .text_web_browser import VisitTool

# Global store for all URLs visited during research
_all_visited_urls = []


def reset_url_store():
    """
    Reset the global URL store
    """
    _all_visited_urls.clear()


def add_url_to_references(url: str, title: str | None = None, date: str | None = None) -> None:
    """
    Add a URL to the global references collection.

    Args:
        url: URL of the source
        title: Title of the source (optional)
        date: Publication date (optional)

    """
    # Don't add duplicates
    if url not in [u.get("url") for u in _all_visited_urls]:
        _all_visited_urls.append(
            {"url": url, "title": title or url, "date": date or "n.d.", "timestamp": int(time.time())}
        )


class CitationAwareGoogleSearchTool(GoogleSearchTool):
    """
    Extension of GoogleSearchTool that collects URL information.
    """

    def forward(self, query: str, filter_year: int | None = None) -> str:
        """
        Perform a Google search and collect URL information.

        Args:
            query: Search query
            filter_year: Optional year filter

        Returns:
            Original search results

        """
        original_results = super().forward(query, filter_year)

        # Extract URLs from search results
        urls = re.findall(r"\[.*?\]\((https?://.*?)\)", original_results)
        title_url_matches = re.findall(r"\[(.*?)\]\((https?://.*?)\)", original_results)

        for match in title_url_matches:
            title, url = match
            add_url_to_references(url=url, title=title)

        for url in urls:
            if url not in [u.get("url") for u in _all_visited_urls]:
                add_url_to_references(url=url)

        return original_results


class CitationAwareVisitTool(VisitTool):
    """
    Extension of VisitTool that collects URL information.
    """

    def forward(self, url: str) -> str:
        """
        Visit a webpage and collect URL information.

        Args:
            url: URL to visit

        Returns:
            Original webpage content

        """
        # Get original content
        original_content = super().forward(url)

        title_match = (
            re.search(r"<title>(.*?)</title>", original_content)
            or re.search(r"<h1>(.*?)</h1>", original_content)
            or re.search(r"# (.*?)$", original_content, re.MULTILINE)
        )
        title = title_match.group(1) if title_match else None

        add_url_to_references(url=url, title=title)
        return original_content


def extract_citations(_text: str) -> dict[str, dict[str, str]]:
    """
    Maintained for backward compatibility.
    It no longer extracts citations but returns an empty dict.

    Args:
        _text: Text to analyze (unused)

    Returns:
        Empty dictionary

    """
    return {}


def create_references_section() -> str:
    """
    Create a properly formatted references section from all collected URLs.

    Returns:
        Formatted references section

    """
    if not _all_visited_urls:
        return ""

    references = ["## References"]

    # Sort references by timestamp (newest first)
    sorted_urls = sorted(_all_visited_urls, key=lambda x: x["timestamp"], reverse=True)

    for i, url_info in enumerate(sorted_urls, 1):
        title = url_info.get("title", url_info["url"])
        url = url_info["url"]
        date = url_info.get("date", "n.d.")

        # Format reference
        reference = f"{i}. *{title}*. Retrieved on {date} from [{url}]({url})"
        references.append(reference)

    return "\n\n".join(references)


def add_visited_url(url: str) -> None:
    """
    Add a URL to the global visited URLs list.
    """
    # Don't add duplicates
    if url not in _all_visited_urls:
        _all_visited_urls.append(url)
