import re
import string
import warnings


def normalize_number_str(number_str: str) -> float:
    """
    Normalize a number string by removing common units and commas.

    Args:
        number_str: str, the number string to normalize
    Returns:
        float, the normalized number

    """
    # we replace these common units and commas to allow
    # conversion to float
    for char in ["$", "%", ","]:
        number_str = number_str.replace(char, "")
    try:
        return float(number_str)
    except ValueError:
        return float("inf")


def split_string(
    s: str,
    char_list: list[str] | None = None,
) -> list[str]:
    """
    Split a string into a list of elements based on specified delimiters.

    Args:
        s: str, the string to split
        char_list: list of str, delimiters to use for splitting (default: [",", ";"])

    Returns:
        list of str, the split elements

    """
    if char_list is None:
        char_list = [",", ";"]
    pattern = f"[{''.join(char_list)}]"
    return re.split(pattern, s)


def is_float(element: any) -> bool:
    """
    Check if the element can be converted to a float.

    Args:
        element: any, the element to check

    Returns:
        bool, True if the element can be converted to a float, False otherwise

    """
    try:
        float(element)
    except ValueError:
        return False
    else:
        return True


def question_scorer(
    model_answer: str,
    ground_truth: str,
) -> bool:
    """
    Compare the model answer with the ground truth.

    Args:
        model_answer: str, the answer generated by the model
        ground_truth: str, the correct answer

    Returns:
        bool, True if the model answer is correct, False otherwise

    """
    # if gt is a number
    if is_float(ground_truth):
        normalized_answer = normalize_number_str(str(model_answer))
        return normalized_answer == float(ground_truth)

    # if gt is a list
    if any(char in ground_truth for char in [",", ";"]):
        gt_elems = split_string(ground_truth)
        ma_elems = split_string(model_answer)

        if len(gt_elems) != len(ma_elems):
            warnings.warn("Answer lists have different lengths, returning False.", UserWarning, stacklevel=2)
            return False

        comparisons = []
        for ma_elem, gt_elem in zip(ma_elems, gt_elems, strict=False):
            if is_float(gt_elem):
                normalized_ma_elem = normalize_number_str(ma_elem)
                comparisons.append(normalized_ma_elem == float(gt_elem))
            else:
                # we do not remove punct since comparisons can include punct
                comparisons.append(
                    normalize_str(ma_elem, remove_punct=False) == normalize_str(gt_elem, remove_punct=False)
                )
        return all(comparisons)

    # if gt is a str
    return normalize_str(model_answer, remove_punct=True) == normalize_str(ground_truth, remove_punct=True)


def check_prediction_contains_answer_letters_in_order(prediction: str, true_answer: str) -> bool:
    """
    Check if the prediction contains the letters of the true answer in order.

    Args:
        prediction: str, the predicted answer
        true_answer: str, the correct answer

    Returns:
        bool, True if the prediction contains the letters of the true answer in order, False otherwise

    """
    prediction = prediction.lower()
    true_answer = true_answer.lower()
    if len(prediction) > len(true_answer) * 3:
        return False
    i = 0
    for letter in true_answer:
        if letter in prediction[i:]:
            i += prediction[i:].index(letter)
        else:
            return False
    return True


def check_close_call(prediction: str, true_answer: str, *, is_correct: bool) -> bool:
    """
    Check if the prediction is a close call to the true answer.

    Args:
        prediction: str, the predicted answer
        true_answer: str, the correct answer
        is_correct: bool, whether the prediction is correct

    Returns:
        bool, True if the prediction is a close call to the true answer, False otherwise

    """
    if is_correct:
        return True
    if is_float(true_answer):
        return is_correct
    return bool(
        check_prediction_contains_answer_letters_in_order(str(prediction), str(true_answer))
        and len(str(true_answer)) * 0.5 <= len(str(prediction)) <= len(str(true_answer)) * 2
    )


def normalize_str(input_str: str, *, remove_punct: bool = True) -> str:
    """
    Normalize a string by:
    - Removing all white spaces
    - Optionally removing punctuation (if remove_punct is True)
    - Converting to lowercase
    Args:
        input_str: str, the string to normalize
        remove_punct: bool, whether to remove punctuation (default: True)

    Returns:
        str, the normalized string

    """
    # Remove all white spaces. Required e.g for seagull vs. sea gull
    no_spaces = re.sub(r"\s", "", input_str)

    # Remove punctuation, if specified.
    if remove_punct:
        translator = str.maketrans("", "", string.punctuation)
        return no_spaces.lower().translate(translator)
    return no_spaces.lower()
