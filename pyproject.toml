[project]
name = "mxgo"
version = "0.1.0"
description = "AI driven email assistant"
authors = [
    {name = "<PERSON><PERSON><PERSON><PERSON>",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12,<3.14"
dependencies = [
    "fastapi (>=0.115.11,<0.116.0)",
    "uvicorn (>=0.34.0,<0.35.0)",
    "logfire (>=3.8.1,<4.0.0)",
    "litellm (>=1.70.0,<2.0.0)",
    "python-dotenv (>=1.0.1,<2.0.0)",
    "loguru (>=0.7.3,<0.8.0)",
    "boto3 (>=1.34.69,<2.0.0)",
    "aiohttp (>=3.11.14,<4.0.0)",
    "smolagents (==1.16.1)",
    "mammoth (>=1.9.0,<2.0.0)",
    "pdfminer-six (>=20240706,<20240707)",
    "python-pptx (>=1.0.2,<2.0.0)",
    "python-docx (>=1.1.2,<2.0.0)",
    "pydub (>=0.25.1,<0.26.0)",
    "puremagic (>=1.28,<2.0)",
    "speechrecognition (>=3.14.2,<4.0.0)",
    "youtube-transcript-api (>=1.0.2,<2.0.0)",
    "openpyxl (>=3.1.5,<4.0.0)",
    "markdown (>=3.7,<4.0)",
    "dramatiq[rabbitmq,watch] (>=1.17.1,<2.0.0)",
    "pathvalidate (>=3.2.3,<4.0.0)",
    "serpapi (>=0.1.5,<0.2.0)",
    "google-search-results (>=2.4.2,<3.0.0)",
    "aiofiles (>=24.1.0,<25.0.0)",
    "supabase (>=2.15.0,<3.0.0)",
    "duckduckgo-search (>=8.0.0,<8.0.2)",
    "requests (>=2.32.3,<3.0.0)",
    "markdownify (>=1.1.0,<2.0.0)",
    "ics (>=0.7.2,<0.8.0)",
    "pandas (>=2.2.3,<3.0.0)",
    "jinja2 (>=3.1.6,<4.0.0)",
    "pydantic[email] (>=2.11.4,<3.0.0)",
    "python-multipart (>=0.0.20,<0.0.21)",
    "wikipedia-api (>=0.8.1,<0.9.0)",
    "markdown2 (>=2.5.3,<3.0.0)",
    "weasyprint (>=65.1,<66.0)",
    "reportlab (>=4.4.1,<5.0.0)",
    "sqlmodel (>=0.0.24,<0.0.25)",
    "alembic (>=1.16.1,<2.0.0)",
    "psycopg2-binary (>=2.9.10,<3.0.0)",
    "asyncpg (>=0.30.0,<0.31.0)",
    "alembic-postgresql-enum (>=1.7.0,<2.0.0)",
    "pydantic-settings (>=2.0.0,<3.0.0)",
    "croniter (>=6.0.0,<7.0.0)",
    "apscheduler[sqlalchemy] (>=3.10.4,<4.0.0)",
    "httpx (>=0.27.0,<1.0.0)",
    "httpx-aiohttp (>=0.1.6,<0.2.0)",
    "transformers (>=4.53.1,<5.0.0)",
    "pyjwt[crypto] (>=2.8.0,<3.0.0)",
]

[tool.ruff]
line-length = 120
target-version = "py313"

[tool.ruff.lint]
select = [
    "ALL", # include all the rules, including new ones
]
ignore = [
    #### modules
    "ANN", # flake8-annotations
    "COM", # flake8-commas
    "C90", # mccabe complexity
    "DJ", # django
    "EXE", # flake8-executable
    "T10", # debugger
    "TID", # flake8-tidy-imports

    #### specific rules
    "BLE001", # Do not catch blind exception: `Exception`
    "D100", # ignore missing docs
    "D101",
    "D102",
    "D103",
    "D104",
    "D105",
    "D106",
    "D107",
    "D200",
    "D203", # conflicts with ruff-format
    "D205",
    "D211",
    "D212",
    "D400",
    "D401",
    "D415",
    "E402", # false positives for local imports
    "E501", # line too long
    "ERA001", # Commented out code
    "G004",
    "PGH003", # Use specific rule codes when ignoring type issues"
    "PLE1205", # Too many arguments for `logging` format string
    "TRY301", # Abstract `raise` to an inner function
    "TRY201", # Use `raise` without specifying exception name
    "TRY003", # external messages in exceptions are too verbose
    "TRY400",
    "TD002",
    "TD003",
    "FIX002", # too verbose descriptions of todos
    "PLR0913", # Too many arguments in function definition
    "UP017", # incompatible with python 3.10 or lower
    "UP007", # incompatible with python 3.10 or lower
]

[tool.ruff.lint.pylint]
max-statements = 75

[tool.ruff.lint.extend-per-file-ignores]
"tests/**.py" = [
    # at least this three should be fine in tests:
    "S101", # asserts allowed in tests...
    "ARG", # Unused function args -> fixtures nevertheless are functionally relevant...
    "FBT", # Don't care about booleans as positional arguments in tests, e.g. via @pytest.mark.parametrize()
    # The below are debateable
    "PLR2004", # Magic value used in comparison, ...
    "S311", # Standard pseudo-random generators are not suitable for cryptographic purposee
    "SLF001" # SLF001 Private member accessed: `_run`
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.group.dev.dependencies]
flake8 = "^7.1.2"
autoflake = "^2.3.1"
ruff = "^0.12.1"
watchdog = "^6.0.0"
pre-commit = "^4.2.0"


[tool.poetry.group.test.dependencies]
locust = "^2.33.2"
psutil = "^7.0.0"
faker = "^37.1.0"
pytest = "^8.3.5"
pytest-asyncio = "^0.26.0"
pytest-sugar = "^1.0.0"
pytest-cov = "^6.2.1"
pytest-timeout = "^2.4.0"
pytest-durations = "^1.5.2"
pytest-retry = "^1.7.0"
fakeredis = "^2.29.0"
freezegun = "^1.5.2"

[tool.pytest.ini_options]
asyncio_mode = "strict"
asyncio_default_fixture_loop_scope = "function"
