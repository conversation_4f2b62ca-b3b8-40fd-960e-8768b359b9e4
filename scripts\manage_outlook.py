#!/usr/bin/env python3
"""
MXGo Outlook Management CLI Tool
Provides command-line interface for managing Outlook integration.
"""

import asyncio
import os
import sys
from typing import List, Dict, Any

import click
from dotenv import load_dotenv

# Add parent directory to path to import mxgo modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from mxgo.email_service_adapter import EmailServiceAdapter
from mxgo.outlook_service import OutlookService
from mxgo.oauth_helper import OAuthHelper

# Load environment variables
load_dotenv()


@click.group()
def cli():
    """MXGo Outlook Management CLI Tool"""
    pass


@cli.command()
def status():
    """Check current email provider status"""
    try:
        adapter = EmailServiceAdapter()
        
        click.echo("📧 Email Provider Status")
        click.echo("=" * 30)
        click.echo(f"Provider: {adapter.get_provider()}")
        click.echo(f"Is Outlook: {adapter.is_outlook_provider()}")
        click.echo(f"Is AWS SES: {adapter.is_aws_ses_provider()}")
        
        if adapter.is_outlook_provider():
            selected_mailbox = os.getenv("OUTLOOK_SELECTED_MAILBOX")
            click.echo(f"Selected Mailbox: {selected_mailbox or 'Not set'}")
        
    except Exception as e:
        click.echo(f"❌ Error: {str(e)}", err=True)


@cli.command()
def auth():
    """Authenticate with Microsoft Graph API"""
    try:
        click.echo("🔐 Starting Microsoft Graph Authentication...")
        
        oauth_helper = OAuthHelper()
        
        # Start interactive authentication
        token = oauth_helper.start_interactive_auth()
        
        if token:
            click.echo("✅ Authentication successful!")
            
            # Test the token
            click.echo("🧪 Testing access token...")
            user_info = asyncio.run(oauth_helper.test_token())
            click.echo(f"👤 Authenticated as: {user_info.get('displayName')} ({user_info.get('mail')})")
            
            # Save token option
            if click.confirm("💾 Save access token to .env file?"):
                oauth_helper.save_token_to_env()
                click.echo("✅ Token saved to .env file")
        else:
            click.echo("❌ Authentication failed")
            
    except Exception as e:
        click.echo(f"❌ Error: {str(e)}", err=True)


@cli.command()
@click.option('--limit', default=5, help='Number of mailboxes to display')
def mailboxes(limit):
    """List available Outlook mailboxes"""
    try:
        adapter = EmailServiceAdapter()
        
        if not adapter.is_outlook_provider():
            click.echo("❌ This command requires Outlook email provider", err=True)
            return
        
        click.echo("📮 Getting available mailboxes...")
        
        # Authenticate and get mailboxes
        asyncio.run(adapter.authenticate())
        mailboxes_list = asyncio.run(adapter.get_mailboxes())
        
        click.echo("\n📧 Available Mailboxes:")
        click.echo("=" * 40)
        
        for i, mailbox in enumerate(mailboxes_list[:limit]):
            click.echo(f"{i+1}. {mailbox['email']}")
            click.echo(f"   Name: {mailbox.get('displayName', 'N/A')}")
            click.echo(f"   Type: {mailbox.get('type', 'N/A')}")
            click.echo()
        
        if len(mailboxes_list) > limit:
            click.echo(f"... and {len(mailboxes_list) - limit} more")
            
    except Exception as e:
        click.echo(f"❌ Error: {str(e)}", err=True)


@cli.command()
@click.argument('email')
def select(email):
    """Select a mailbox for email operations"""
    try:
        adapter = EmailServiceAdapter()
        
        if not adapter.is_outlook_provider():
            click.echo("❌ This command requires Outlook email provider", err=True)
            return
        
        # Set selected mailbox
        adapter.set_selected_mailbox(email)
        
        # Update .env file
        env_file = ".env"
        env_lines = []
        
        if os.path.exists(env_file):
            with open(env_file, 'r') as f:
                env_lines = f.readlines()
        
        # Update or add mailbox line
        mailbox_line = f"OUTLOOK_SELECTED_MAILBOX={email}\n"
        mailbox_found = False
        
        for i, line in enumerate(env_lines):
            if line.startswith("OUTLOOK_SELECTED_MAILBOX="):
                env_lines[i] = mailbox_line
                mailbox_found = True
                break
        
        if not mailbox_found:
            env_lines.append(mailbox_line)
        
        # Write back to file
        with open(env_file, 'w') as f:
            f.writelines(env_lines)
        
        click.echo(f"✅ Selected mailbox: {email}")
        click.echo(f"💾 Updated .env file")
        
    except Exception as e:
        click.echo(f"❌ Error: {str(e)}", err=True)


@cli.command()
@click.option('--mailbox', help='Specific mailbox to check')
@click.option('--folder', default='inbox', help='Folder to check')
@click.option('--limit', default=5, help='Number of emails to display')
def emails(mailbox, folder, limit):
    """List recent emails from Outlook"""
    try:
        adapter = EmailServiceAdapter()
        
        if not adapter.is_outlook_provider():
            click.echo("❌ This command requires Outlook email provider", err=True)
            return
        
        click.echo(f"📬 Getting emails from {folder}...")
        
        # Authenticate and get emails
        asyncio.run(adapter.authenticate())
        emails_list = asyncio.run(adapter.get_emails(mailbox, folder, limit))
        
        click.echo(f"\n📧 Recent Emails ({len(emails_list)}):")
        click.echo("=" * 50)
        
        for i, email in enumerate(emails_list):
            click.echo(f"{i+1}. {email.get('subject', 'No Subject')}")
            click.echo(f"   From: {email.get('from', {}).get('emailAddress', {}).get('address', 'Unknown')}")
            click.echo(f"   Date: {email.get('receivedDateTime', 'Unknown')}")
            click.echo(f"   ID: {email.get('id', 'Unknown')}")
            click.echo()
            
    except Exception as e:
        click.echo(f"❌ Error: {str(e)}", err=True)


@cli.command()
@click.option('--to', required=True, help='Recipient email address')
@click.option('--subject', required=True, help='Email subject')
@click.option('--body', required=True, help='Email body text')
@click.option('--html', help='HTML body (optional)')
def send(to, subject, body, html):
    """Send a test email via Outlook"""
    try:
        adapter = EmailServiceAdapter()
        
        if not adapter.is_outlook_provider():
            click.echo("❌ This command requires Outlook email provider", err=True)
            return
        
        click.echo(f"📤 Sending email to {to}...")
        
        # Authenticate and send email
        asyncio.run(adapter.authenticate())
        result = asyncio.run(adapter.send_email(
            to_address=to,
            subject=subject,
            body_text=body,
            body_html=html
        ))
        
        if result.get("status") == "success":
            click.echo("✅ Email sent successfully!")
            click.echo(f"📧 Message ID: {result.get('message_id', 'Unknown')}")
        else:
            click.echo(f"❌ Failed to send email: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        click.echo(f"❌ Error: {str(e)}", err=True)


@cli.command()
def test():
    """Run comprehensive tests of Outlook integration"""
    try:
        click.echo("🧪 Running Outlook Integration Tests")
        click.echo("=" * 40)
        
        # Test 1: Check provider configuration
        click.echo("1. Checking email provider configuration...")
        adapter = EmailServiceAdapter()
        
        if not adapter.is_outlook_provider():
            click.echo("❌ Email provider is not set to 'outlook'")
            click.echo("   Please set EMAIL_PROVIDER=outlook in .env file")
            return
        
        click.echo("✅ Email provider correctly set to Outlook")
        
        # Test 2: Check credentials
        click.echo("\n2. Checking Microsoft Graph credentials...")
        client_id = os.getenv("MICROSOFT_CLIENT_ID")
        client_secret = os.getenv("MICROSOFT_CLIENT_SECRET")
        
        if not client_id or not client_secret:
            click.echo("❌ Microsoft Graph credentials missing")
            click.echo("   Please set MICROSOFT_CLIENT_ID and MICROSOFT_CLIENT_SECRET")
            return
        
        click.echo("✅ Microsoft Graph credentials found")
        
        # Test 3: Test authentication
        click.echo("\n3. Testing authentication...")
        try:
            await_result = asyncio.run(adapter.authenticate())
            click.echo("✅ Authentication successful")
        except Exception as e:
            click.echo(f"❌ Authentication failed: {str(e)}")
            return
        
        # Test 4: Test mailbox access
        click.echo("\n4. Testing mailbox access...")
        try:
            mailboxes_list = asyncio.run(adapter.get_mailboxes())
            click.echo(f"✅ Found {len(mailboxes_list)} mailboxes")
            for mailbox in mailboxes_list:
                click.echo(f"   📮 {mailbox['email']}")
        except Exception as e:
            click.echo(f"❌ Mailbox access failed: {str(e)}")
            return
        
        # Test 5: Test email reading
        click.echo("\n5. Testing email reading...")
        try:
            emails_list = asyncio.run(adapter.get_emails(limit=1))
            click.echo(f"✅ Successfully read {len(emails_list)} emails")
        except Exception as e:
            click.echo(f"❌ Email reading failed: {str(e)}")
        
        click.echo("\n🎉 All tests completed!")
        
    except Exception as e:
        click.echo(f"❌ Test error: {str(e)}", err=True)


@cli.command()
def config():
    """Show current configuration"""
    try:
        click.echo("⚙️ Current Configuration")
        click.echo("=" * 30)
        
        # Email provider
        email_provider = os.getenv("EMAIL_PROVIDER", "Not set")
        click.echo(f"Email Provider: {email_provider}")
        
        # Microsoft Graph settings
        client_id = os.getenv("MICROSOFT_CLIENT_ID", "Not set")
        client_secret = os.getenv("MICROSOFT_CLIENT_SECRET", "Not set")
        tenant_id = os.getenv("MICROSOFT_TENANT_ID", "common")
        selected_mailbox = os.getenv("OUTLOOK_SELECTED_MAILBOX", "Not set")
        
        click.echo(f"Microsoft Client ID: {client_id[:20]}..." if len(client_id) > 20 else client_id)
        click.echo(f"Microsoft Client Secret: {'Set' if client_secret != 'Not set' else 'Not set'}")
        click.echo(f"Microsoft Tenant ID: {tenant_id}")
        click.echo(f"Selected Mailbox: {selected_mailbox}")
        
        # SiliconFlow settings
        siliconflow_key = os.getenv("SILICONFLOW_API_KEY", "Not set")
        click.echo(f"SiliconFlow API Key: {'Set' if siliconflow_key != 'Not set' else 'Not set'}")
        
        # API settings
        api_key = os.getenv("X_API_KEY", "Not set")
        click.echo(f"API Key: {'Set' if api_key != 'Not set' else 'Not set'}")
        
    except Exception as e:
        click.echo(f"❌ Error: {str(e)}", err=True)


if __name__ == '__main__':
    cli()
