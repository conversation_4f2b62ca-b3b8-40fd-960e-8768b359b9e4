#!/usr/bin/env python3
"""
Generate a test JWT token for testing the /suggestions endpoint.
"""

import os
from datetime import datetime, timedelta, timezone

import jwt

# JWT configuration
JWT_SECRET = os.getenv("JWT_SECRET", "test_secret_key_for_development_only")
JWT_ALGORITHM = "HS256"


def generate_test_jwt(email: str = "<EMAIL>", user_id: str = "test_user_123") -> str:
    """Generate a test JWT token."""
    # Token expires in 1 hour
    exp = datetime.now(timezone.utc) + timedelta(hours=1)

    payload = {
        "sub": user_id,  # Subject (user ID)
        "email": email,
        "exp": int(exp.timestamp()),
        "iat": int(datetime.now(timezone.utc).timestamp()),  # Issued at
    }

    return jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)


if __name__ == "__main__":
    token = generate_test_jwt()
