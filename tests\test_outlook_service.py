"""
Test cases for Outlook service integration.
"""

import os
import pytest
from unittest.mock import Mock, patch, AsyncMock

from mxgo.outlook_service import OutlookService
from mxgo.email_service_adapter import EmailServiceAdapter, EmailProvider


class TestOutlookService:
    """Test the OutlookService class functionality."""

    @patch.dict(
        os.environ,
        {
            "MICROSOFT_CLIENT_ID": "test_client_id",
            "MICROSOFT_CLIENT_SECRET": "test_client_secret",
            "MICROSOFT_TENANT_ID": "test_tenant_id",
        },
    )
    def test_outlook_service_init_success(self):
        """Test successful OutlookService initialization."""
        service = OutlookService()
        
        assert service.client_id == "test_client_id"
        assert service.client_secret == "test_client_secret"
        assert service.tenant_id == "test_tenant_id"
        assert service.authority == "https://login.microsoftonline.com/test_tenant_id"

    def test_outlook_service_init_missing_credentials(self):
        """Test OutlookService initialization with missing credentials."""
        with pytest.raises(ValueError, match="Microsoft Graph API credentials are required"):
            OutlookService()

    @patch.dict(
        os.environ,
        {
            "MICROSOFT_CLIENT_ID": "test_client_id",
            "MICROSOFT_CLIENT_SECRET": "test_client_secret",
        },
    )
    @patch("msal.ConfidentialClientApplication")
    @pytest.mark.asyncio
    async def test_get_access_token_success(self, mock_msal_app):
        """Test successful access token acquisition."""
        mock_app_instance = Mock()
        mock_app_instance.acquire_token_for_client.return_value = {
            "access_token": "test_access_token"
        }
        mock_msal_app.return_value = mock_app_instance
        
        service = OutlookService()
        token = await service.get_access_token()
        
        assert token == "test_access_token"
        assert service.access_token == "test_access_token"

    @patch.dict(
        os.environ,
        {
            "MICROSOFT_CLIENT_ID": "test_client_id",
            "MICROSOFT_CLIENT_SECRET": "test_client_secret",
        },
    )
    @patch("msal.ConfidentialClientApplication")
    @pytest.mark.asyncio
    async def test_get_access_token_failure(self, mock_msal_app):
        """Test access token acquisition failure."""
        mock_app_instance = Mock()
        mock_app_instance.acquire_token_for_client.return_value = {
            "error": "invalid_client",
            "error_description": "Invalid client credentials"
        }
        mock_msal_app.return_value = mock_app_instance
        
        service = OutlookService()
        
        with pytest.raises(Exception, match="Authentication failed"):
            await service.get_access_token()

    @patch.dict(
        os.environ,
        {
            "MICROSOFT_CLIENT_ID": "test_client_id",
            "MICROSOFT_CLIENT_SECRET": "test_client_secret",
        },
    )
    @patch("msal.ConfidentialClientApplication")
    @patch("httpx.AsyncClient")
    @pytest.mark.asyncio
    async def test_send_email_success(self, mock_httpx_client, mock_msal_app):
        """Test successful email sending."""
        # Mock MSAL app
        mock_app_instance = Mock()
        mock_app_instance.acquire_token_for_client.return_value = {
            "access_token": "test_access_token"
        }
        mock_msal_app.return_value = mock_app_instance
        
        # Mock HTTP client
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"id": "test_message_id"}
        mock_response.content = b'{"id": "test_message_id"}'
        
        mock_client_instance = AsyncMock()
        mock_client_instance.post.return_value = mock_response
        mock_httpx_client.return_value.__aenter__.return_value = mock_client_instance
        
        service = OutlookService()
        await service.get_access_token()
        
        result = await service.send_email(
            to_addresses=["<EMAIL>"],
            subject="Test Subject",
            body_text="Test body"
        )
        
        assert result["status"] == "success"
        assert "message_id" in result

    @patch.dict(
        os.environ,
        {
            "MICROSOFT_CLIENT_ID": "test_client_id",
            "MICROSOFT_CLIENT_SECRET": "test_client_secret",
        },
    )
    @patch("msal.ConfidentialClientApplication")
    @patch("httpx.AsyncClient")
    @pytest.mark.asyncio
    async def test_get_user_mailboxes(self, mock_httpx_client, mock_msal_app):
        """Test getting user mailboxes."""
        # Mock MSAL app
        mock_app_instance = Mock()
        mock_app_instance.acquire_token_for_client.return_value = {
            "access_token": "test_access_token"
        }
        mock_msal_app.return_value = mock_app_instance
        
        # Mock HTTP client responses
        user_response = Mock()
        user_response.status_code = 200
        user_response.json.return_value = {
            "mail": "<EMAIL>",
            "displayName": "Test User",
            "id": "user_id_123"
        }
        user_response.content = b'{"mail": "<EMAIL>"}'
        
        folders_response = Mock()
        folders_response.status_code = 200
        folders_response.json.return_value = {"value": []}
        folders_response.content = b'{"value": []}'
        
        mock_client_instance = AsyncMock()
        mock_client_instance.get.side_effect = [user_response, folders_response]
        mock_httpx_client.return_value.__aenter__.return_value = mock_client_instance
        
        service = OutlookService()
        await service.get_access_token()
        
        mailboxes = await service.get_user_mailboxes()
        
        assert len(mailboxes) == 1
        assert mailboxes[0]["email"] == "<EMAIL>"
        assert mailboxes[0]["displayName"] == "Test User"
        assert mailboxes[0]["type"] == "primary"


class TestEmailServiceAdapter:
    """Test the EmailServiceAdapter class functionality."""

    @patch.dict(os.environ, {"EMAIL_PROVIDER": "outlook"})
    @patch("mxgo.email_service_adapter.OutlookService")
    def test_adapter_init_outlook(self, mock_outlook_service):
        """Test adapter initialization with Outlook provider."""
        adapter = EmailServiceAdapter()
        
        assert adapter.provider == EmailProvider.OUTLOOK.value
        assert adapter.is_outlook_provider()
        assert not adapter.is_aws_ses_provider()
        mock_outlook_service.assert_called_once()

    @patch.dict(os.environ, {"EMAIL_PROVIDER": "aws_ses"})
    @patch("mxgo.email_service_adapter.EmailSender")
    def test_adapter_init_aws_ses(self, mock_email_sender):
        """Test adapter initialization with AWS SES provider."""
        adapter = EmailServiceAdapter()
        
        assert adapter.provider == EmailProvider.AWS_SES.value
        assert adapter.is_aws_ses_provider()
        assert not adapter.is_outlook_provider()
        mock_email_sender.assert_called_once()

    def test_adapter_init_invalid_provider(self):
        """Test adapter initialization with invalid provider."""
        with pytest.raises(ValueError, match="Unsupported email provider"):
            EmailServiceAdapter(provider="invalid_provider")

    @patch.dict(os.environ, {"EMAIL_PROVIDER": "outlook"})
    @patch("mxgo.email_service_adapter.OutlookService")
    @pytest.mark.asyncio
    async def test_adapter_send_email_outlook(self, mock_outlook_service):
        """Test sending email through Outlook adapter."""
        mock_service_instance = AsyncMock()
        mock_service_instance.send_email.return_value = {
            "status": "success",
            "message_id": "test_id"
        }
        mock_outlook_service.return_value = mock_service_instance
        
        adapter = EmailServiceAdapter()
        
        result = await adapter.send_email(
            to_address="<EMAIL>",
            subject="Test Subject",
            body_text="Test body"
        )
        
        assert result["status"] == "success"
        mock_service_instance.send_email.assert_called_once()

    @patch.dict(os.environ, {"EMAIL_PROVIDER": "outlook"})
    @patch("mxgo.email_service_adapter.OutlookService")
    @pytest.mark.asyncio
    async def test_adapter_get_emails_outlook(self, mock_outlook_service):
        """Test getting emails through Outlook adapter."""
        mock_service_instance = AsyncMock()
        mock_service_instance.get_emails.return_value = [
            {"id": "email1", "subject": "Test Email 1"},
            {"id": "email2", "subject": "Test Email 2"}
        ]
        mock_outlook_service.return_value = mock_service_instance
        
        adapter = EmailServiceAdapter()
        
        emails = await adapter.get_emails(limit=2)
        
        assert len(emails) == 2
        assert emails[0]["subject"] == "Test Email 1"
        mock_service_instance.get_emails.assert_called_once_with(None, "inbox", 2)

    @patch.dict(os.environ, {"EMAIL_PROVIDER": "aws_ses"})
    @patch("mxgo.email_service_adapter.EmailSender")
    @pytest.mark.asyncio
    async def test_adapter_get_emails_aws_ses(self, mock_email_sender):
        """Test getting emails through AWS SES adapter (should return empty)."""
        adapter = EmailServiceAdapter()
        
        emails = await adapter.get_emails()
        
        assert emails == []

    @patch.dict(os.environ, {"EMAIL_PROVIDER": "outlook"})
    @patch("mxgo.email_service_adapter.OutlookService")
    def test_adapter_set_selected_mailbox(self, mock_outlook_service):
        """Test setting selected mailbox."""
        mock_service_instance = Mock()
        mock_outlook_service.return_value = mock_service_instance
        
        adapter = EmailServiceAdapter()
        adapter.set_selected_mailbox("<EMAIL>")
        
        mock_service_instance.set_selected_mailbox.assert_called_once_with("<EMAIL>")
